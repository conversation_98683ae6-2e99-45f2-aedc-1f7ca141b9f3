import React, { useState, useEffect, createContext } from "react";
import CalendarContainer from "./CalendarContainer";
import DataContainer from "./DataContainer";
import "./styles.css";
import { Tabs, Spin } from "antd";
import { ImportOutlined, ExportOutlined } from "@ant-design/icons";
import moment from "moment";
import { UrlParams } from "./Filter";
import {
  getCalendarYear,
  getCalendarMonth,
  getCalendarMonthNurse,
  getCalendarMonthPmOrder,
  getCalendarMonthMeasuring,
  getCalendarDataAll,
  getCalendarDataNursel,
  getCalendarDataPmOrder,
  getCalendarDataMeasuring,
} from "@/service";
export const calendarContext = createContext({});
const PAGE_DATA = {
  YearCalendarData: [],
  MonthCalendarData: [],
  MonthClinicalData: [],
  MonthPmOrderData: [],
  MonthMeasuringData: [],
};
const App = () => {
  // 使用 ref 来跟踪是否已经执行过初始数据请求
  const initialDataFetchedRef = React.useRef(false);
  // 添加一个请求锁，防止短时间内重复请求
  const requestLockRef = React.useRef(false);

  const [tab, setTab] = useState(UrlParams.getTab());
  const [currentDate, setCurrentDate] = useState(new Date(UrlParams.getCurrentDate()));
  const [mode, setMode] = useState(UrlParams.getMode());
  const [filter, setFilter] = useState(UrlParams.getFilter());
  const [startDate, setStartDate] = useState(UrlParams.getStartDate());
  const [endDate, setEndDate] = useState(UrlParams.getEndDate());
  const [data, setData] = useState(PAGE_DATA);
  const [loading, setLoading] = useState(false);

  const getYearData = async date => {
    const calendarYearData = await getCalendarYear(date);
    setData(data => ({
      ...data,
      YearCalendarData: calendarYearData,
    }));
  };
  const getMonthData = async date => {
    const [
      calendarMonthDataResult,
      calendarMonthNurseDataResult,
      calendarMonthPmOrderDataResult,
      calendarMonthMeasuringDataResult,
    ] = await Promise.allSettled([
      getCalendarMonth(date),
      getCalendarMonthNurse(date),
      getCalendarMonthPmOrder(date),
      getCalendarMonthMeasuring(date),
    ]);
    setData(data => ({
      ...data,
      MonthCalendarData: calendarMonthDataResult.status === "fulfilled" ? calendarMonthDataResult.value : [],
      MonthClinicalData: calendarMonthNurseDataResult.status === "fulfilled" ? calendarMonthNurseDataResult.value : [],
      MonthPmOrderData:
        calendarMonthPmOrderDataResult.status === "fulfilled" ? calendarMonthPmOrderDataResult.value : [],
      MonthMeasuringData:
        calendarMonthMeasuringDataResult.status === "fulfilled" ? calendarMonthMeasuringDataResult.value : [],
    }));
  };
  const getCalendarDataType = async date => {
    const [
      calendarDataTypeAllResult,
      calendarDataTypeNurselResult,
      calendarDataTypePmOrderResult,
      calendarDataTypeMeasuringResult,
    ] = await Promise.allSettled([
      getCalendarDataAll(date),
      getCalendarDataNursel(date),
      getCalendarDataPmOrder(date),
      getCalendarDataMeasuring(date),
    ]);
    setData(data => ({
      ...data,
      CalendarDataTypeAll: calendarDataTypeAllResult.status === "fulfilled" ? calendarDataTypeAllResult.value : [],
      CalendarDataTypeNursel:
        calendarDataTypeNurselResult.status === "fulfilled" ? calendarDataTypeNurselResult.value : [],
      CalendarDataTypePmOrder:
        calendarDataTypePmOrderResult.status === "fulfilled" ? calendarDataTypePmOrderResult.value : [],
      CalendarDataTypeMeasuring:
        calendarDataTypeMeasuringResult.status === "fulfilled" ? calendarDataTypeMeasuringResult.value : [],
    }));
  };

  // 日历数据请求函数
  const fetchCalendarData = React.useCallback(params => {
    setLoading(true);
    // 如果请求锁已激活，则不执行请求
    if (requestLockRef.current) {
      console.log("请求被锁定，跳过");
      setLoading(false);
      return;
    }

    // 激活请求锁
    requestLockRef.current = true;

    const { mode, startDate, endDate } = params;

    console.log("执行数据请求:", { mode, startDate, endDate });

    if (mode === "year") {
      getYearData({ startDate, endDate }).finally(() => {
        // 请求完成后释放锁
        setTimeout(() => {
          requestLockRef.current = false;
          setLoading(false);
        }, 200); // 添加200ms延迟，防止过快释放
      });
    } else {
      getMonthData({ startDate, endDate }).finally(() => {
        // 请求完成后释放锁
        setTimeout(() => {
          requestLockRef.current = false;
          setLoading(false);
        }, 200); // 添加200ms延迟，防止过快释放
      });
    }
  }, []);
  // 数据tab请求数据函数
  const fetchData = React.useCallback(params => {
    setLoading(true);
    // 如果请求锁已激活，则不执行请求
    if (requestLockRef.current) {
      console.log("请求被锁定，跳过");
      return;
    }
    // 激活请求锁
    requestLockRef.current = true;
    const { startDate, endDate } = params;
    getCalendarDataType({ startDate, endDate }).finally(() => {
      // 请求完成后释放锁
      setTimeout(() => {
        requestLockRef.current = false;
        setLoading(false);
      }, 200); // 添加200ms延迟，防止过快释放
    });
  }, []);

  // 处理运行时逻辑变化
  useEffect(() => {
    // 当 tab 变化时，更新 URL
    UrlParams.set("tab", tab);
    // 如果切换到 calendar 标签页，执行数据请求
    if (tab === "data") {
      console.log("切换到数据标签页，执行数据请求");
      fetchData({ startDate, endDate });
    } else {
      console.log("切换到日历标签页，执行数据请求");
      fetchCalendarData({ mode, startDate, endDate });
    }
  }, [tab]);

  // 处理初始数据加载
  useEffect(() => {
    console.log("组件挂载，执行初始数据请求");
    // 确保只执行一次初始数据请求
    if (!initialDataFetchedRef.current) {
      initialDataFetchedRef.current = true;

      if (tab === "data") {
        console.log("数据tab");
        fetchData({ startDate, endDate });
      } else {
        // 直接执行数据请求
        fetchCalendarData({ mode, startDate, endDate });
      }
    }
  }, []);

  // 统一的 URL 参数变化监听机制
  useEffect(() => {
    // 添加一个防抖机制，避免短时间内多次触发
    let debounceTimer = null;

    const handleUrlChange = () => {
      // 清除之前的定时器
      if (debounceTimer) clearTimeout(debounceTimer);

      // 设置新的定时器，延迟执行
      debounceTimer = setTimeout(() => {
        const newTab = UrlParams.getTab();
        const newMode = UrlParams.getMode();
        const newFilter = UrlParams.getFilter();
        const newStartDate = UrlParams.getStartDate();
        const newEndDate = UrlParams.getEndDate();
        const newCurrentDate = UrlParams.getCurrentDate();

        console.log("检查URL参数变化:", {
          newMode,
          newStartDate,
          newEndDate,
          newTab,
          newFilter,
          newCurrentDate,
        });
        console.log("当前状态:", {
          mode,
          startDate,
          endDate,
          tab,
          filter,
          currentDate: moment(currentDate).format("YYYY-MM-DD"),
        });

        // 检查是否有参数变化
        const hasChanges =
          newMode !== mode ||
          newStartDate !== startDate ||
          newEndDate !== endDate ||
          newTab !== tab ||
          newFilter !== filter ||
          newCurrentDate !== moment(currentDate).format("YYYY-MM-DD");

        if (hasChanges) {
          console.log("URL 参数变化，更新状态并执行数据请求");

          // 更新状态
          setMode(newMode);
          setStartDate(newStartDate);
          setEndDate(newEndDate);
          setTab(newTab);
          setFilter(newFilter);
          setCurrentDate(new Date(newCurrentDate));

          // 根据新的 tab 执行相应的数据请求
          if (newTab === "data") {
            console.log("切换到数据tab，执行数据请求");
            fetchData({ startDate: newStartDate, endDate: newEndDate });
          } else {
            console.log("切换到日历tab，执行数据请求");
            fetchCalendarData({ mode: newMode, startDate: newStartDate, endDate: newEndDate });
          }
        }
      }, 50); // 50毫秒的防抖时间
    };

    // 只有在初始数据已经加载后，才设置 URL 变化监听
    if (initialDataFetchedRef.current) {
      // 监听 popstate 事件（浏览器前进/后退按钮）
      window.addEventListener("popstate", handleUrlChange);

      // 监听 pushState/replaceState（用于程序化的 URL 变化）
      const originalPushState = window.history.pushState;
      const originalReplaceState = window.history.replaceState;

      window.history.pushState = function () {
        originalPushState.apply(this, arguments);
        handleUrlChange();
      };

      window.history.replaceState = function () {
        originalReplaceState.apply(this, arguments);
        handleUrlChange();
      };

      return () => {
        if (debounceTimer) clearTimeout(debounceTimer);
        window.removeEventListener("popstate", handleUrlChange);
        window.history.pushState = originalPushState;
        window.history.replaceState = originalReplaceState;
      };
    }
  }, [mode, startDate, endDate, tab, filter, currentDate, fetchCalendarData, fetchData]);

  const tabBarExtraContent = (
    <div>
      <ImportOutlined style={{ fontSize: 18, marginRight: 16, cursor: "pointer" }} title={""} />
      <ExportOutlined style={{ fontSize: 18, cursor: "pointer" }} title={""} />
    </div>
  );

  return (
    <div className="app-container">
      <calendarContext.Provider
        value={{
          data,
        }}
      >
        <Spin tip="获取数据..." spinning={loading}>
          <Tabs
            activeKey={tab}
            onChange={e => {
              setTab(e);
              // 确保切换 tab 时同步所有时间相关参数
              const currentDateStr = moment(currentDate).format("YYYY-MM-DD");
              UrlParams.setCurrentDate(currentDateStr);

              // 根据当前模式重新计算并同步 startDate 和 endDate
              let newStartDate, newEndDate;
              if (mode === "year") {
                newStartDate = moment(currentDate).startOf("year").format("YYYY-MM-DD");
                newEndDate = moment(currentDate).endOf("year").format("YYYY-MM-DD");
              } else {
                newStartDate = moment(currentDate).startOf("month").format("YYYY-MM-DD");
                newEndDate = moment(currentDate).endOf("month").format("YYYY-MM-DD");
              }

              // 只有当日期范围真正变化时才更新
              if (newStartDate !== startDate || newEndDate !== endDate) {
                setStartDate(newStartDate);
                setEndDate(newEndDate);
                UrlParams.setStartDate(newStartDate);
                UrlParams.setEndDate(newEndDate);
              }
            }}
            tabBarExtraContent={tabBarExtraContent}
            style={{ width: "100%" }}
            items={[
              {
                label: "日历",
                key: "calendar",
                children: <CalendarContainer />,
              },
              {
                label: "数据",
                key: "data",
                children: <DataContainer />,
              },
            ]}
          ></Tabs>
        </Spin>
      </calendarContext.Provider>
    </div>
  );
};

export default App;
