import React, { useState, useEffect } from "react";
import moment from "moment";

// 过滤选项数组
export const filterOptions = [
  { key: "all", label: "全部" },
  { key: "clinical", label: "临床助手" },
  { key: "pm", label: "预防性维护" },
  { key: "metering", label: "计量" },
];

export const UrlParams = {
  // 获取指定参数的值，若不存在或不符合验证条件则返回默认值
  get(param, validValues, defaultValue) {
    const params = new URLSearchParams(window.location.search);
    const value = params.get(param);

    // 验证逻辑：如果提供了有效值列表，则检查是否匹配
    if (validValues && !validValues.includes(value)) {
      return defaultValue;
    }

    return value || defaultValue;
  },

  // 设置指定参数的值，仅在值变化时更新URL（不触发页面刷新）
  set(param, value) {
    const params = new URLSearchParams(window.location.search);

    if (params.get(param) !== value) {
      params.set(param, value);

      // 构建新URL，保留原有路径和hash
      const newUrl = window.location.pathname + "?" + params.toString() + window.location.hash;

      // 使用history API更新URL，不触发页面刷新
      window.history.pushState(null, "", newUrl);
    }
  },

  // 便捷方法：获取视图模式参数 ，默认mode 为year
  getMode() {
    return this.get("mode", ["year", "month"], "year");
  },

  // 便捷方法：设置视图模式参数
  setMode(mode) {
    this.set("mode", mode);
  },

  // 便捷方法：获取标签页参数
  getTab() {
    return this.get("tab", ["calendar", "data"], "calendar");
  },

  // 便捷方法：设置标签页参数
  setTab(tab) {
    this.set("tab", tab);
  },

  getFilter() {
    return this.get(
      "filter",
      filterOptions.map(o => o.key),
      "all",
    );
  },

  setFilter(filter) {
    this.set("filter", filter);
  },

  getStartDate() {
    // 根据mode和currentDate动态计算默认值
    const mode = this.getMode();
    const currentDate = this.getCurrentDate();
    let defaultValue;
    if (mode === "year") {
      defaultValue = moment(currentDate).startOf("year").format("YYYY-MM-DD");
    } else {
      // 默认month
      defaultValue = moment(currentDate).startOf("month").format("YYYY-MM-DD");
    }
    return this.get("startDate", null, defaultValue);
  },

  setStartDate(startDate) {
    this.set("startDate", startDate);
  },

  getEndDate() {
    // 根据mode和currentDate动态计算默认值
    const mode = this.getMode();
    const currentDate = this.getCurrentDate();
    let defaultValue;
    if (mode === "year") {
      defaultValue = moment(currentDate).endOf("year").format("YYYY-MM-DD");
    } else {
      // 默认month
      defaultValue = moment(currentDate).endOf("month").format("YYYY-MM-DD");
    }
    return this.get("endDate", null, defaultValue);
  },

  setEndDate(endDate) {
    this.set("endDate", endDate);
  },

  // 新增：获取 month 参数，默认当前月份
  getMonth() {
    return this.get("month", null, moment().format("MM"));
  },

  // 新增：设置 month 参数
  setMonth(month) {
    this.set("month", month);
  },

  // 新增：获取 year 参数，默认当前年份
  getYear() {
    return this.get("year", null, moment().format("YYYY"));
  },

  // 新增：设置 year 参数
  setYear(year) {
    this.set("year", year);
  },

  // 新增：获取 currentDate 参数，默认今天
  getCurrentDate() {
    return this.get("currentDate", null, moment().format("YYYY-MM-DD"));
  },

  // 新增：设置 currentDate 参数，并自动设置startDate、endDate、year、month
  setCurrentDate(currentDate) {
    this.set("currentDate", currentDate);
    const mode = this.getMode();
    let startDate, endDate;
    if (mode === "year") {
      startDate = moment(currentDate).startOf("year").format("YYYY-MM-DD");
      endDate = moment(currentDate).endOf("year").format("YYYY-MM-DD");
    } else {
      // 默认month
      startDate = moment(currentDate).startOf("month").format("YYYY-MM-DD");
      endDate = moment(currentDate).endOf("month").format("YYYY-MM-DD");
    }
    this.setStartDate(startDate);
    this.setEndDate(endDate);
    // 同步year和month
    this.setYear(moment(currentDate).format("YYYY"));
    this.setMonth(moment(currentDate).format("MM"));
  },
};

export const Filter = props => {
  const { syncUrl = true, listenUrl = true } = props;
  const [filter, setFilter] = useState(UrlParams.getFilter());

  // 如果有onChange回调，初始时调用一次
  useEffect(() => {
    if (syncUrl) {
      setFilter(UrlParams.getFilter());
      props.onChange && props.onChange(UrlParams.getFilter());
    }
  }, []);

  // 同步filter到url
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (syncUrl && params.get("filter") !== filter) {
      UrlParams.setFilter(filter);
    }
  }, [filter, syncUrl]);

  // 监听 URL 变化（简化版本，主要依赖 App.js 的统一监听）
  useEffect(() => {
    if (listenUrl) {
      const handleUrlChange = () => {
        const queryFilter = UrlParams.getFilter();
        if (queryFilter !== filter) {
          setFilter(queryFilter);
          if (props.onChange) props.onChange(queryFilter);
        }
      };

      // 只监听 popstate 事件，其他 URL 变化由 App.js 统一处理
      const onPopState = () => {
        handleUrlChange();
      };

      window.addEventListener("popstate", onPopState);

      return () => {
        window.removeEventListener("popstate", onPopState);
      };
    }
  }, [listenUrl, filter, props]);

  return (
    <select
      className={props.className || "filter-select"}
      value={filter}
      onChange={e => {
        setFilter(e.target.value);
        if (props.onChange) {
          props.onChange(e.target.value);
        }
      }}
    >
      {(props.options || filterOptions).map(option => (
        <option key={option.key} value={option.key}>
          {option.label}
        </option>
      ))}
    </select>
  );
};
