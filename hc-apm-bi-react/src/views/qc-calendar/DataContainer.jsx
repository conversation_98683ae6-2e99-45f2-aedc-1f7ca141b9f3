import React, { useState, useEffect, useRef, useContext, useMemo } from "react";
import { DatePicker, Table } from "antd";
import { utils, writeFile } from "xlsx-js-style";
import { Filter, UrlParams } from "./Filter";
import { calendarContext } from "./App";
import { toDecimal } from "@/urls/utils";
import {
  dataTypeAllColumns,
  dataTypeNurselColumns,
  dataTypePmOrderColumns,
  dataTypeMeasuringColumns,
} from "./DataContainerColumns";
import moment from "moment";
import "./DataContainer.css";

const { RangePicker } = DatePicker;

// 环形百分比组件（简单占位）
const CirclePercent = ({ label, value, color, sub, percentage = 0 }) => {
  return (
    <div className="data-circle-percent">
      <svg width="100" height="100">
        <circle cx="50" cy="50" r="45" stroke="#eee" strokeWidth="5" fill="none" />
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke={color}
          strokeWidth="5"
          fill="none"
          strokeDasharray={2 * Math.PI * 45}
          strokeDashoffset={2 * Math.PI * 45 * (1 - percentage / 100)}
          transform="rotate(-90 50 50)"
        />
        <text x="50" y="40" textAnchor="middle" fontSize="13" fill="#55595E">
          {label}
        </text>
        <text x={sub ? 45 : 50} y="68" textAnchor="middle" fontSize="24" fill={color} fontWeight="bold">
          {typeof value === "number" ? value : percentage}
        </text>
        {sub && (
          <text x="70" y="68" textAnchor="start" fontSize="14" fill={color} fontWeight="bold">
            %
          </text>
        )}
      </svg>
    </div>
  );
};

const DownloadIcon = props => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
    <rect width="20" height="20" rx="4" fill="#F5F5F5" />
    <path
      d="M10 4V13M10 13L6 9M10 13L14 9"
      stroke="#746a95"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="4" y="16" width="12" height="1.5" rx="0.75" fill="#746a95" />
  </svg>
);
const downloadTable2Excel = (tableDom, filter, fileName) => {
  // 表头样式
  const headerStyle = {
    font: {
      sz: 11,
      color: { rgb: "FFFFFF" },
    },
    fill: {
      fgColor: { rgb: "663399" }, // 紫色背景
      patternType: "solid",
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  };
  // 数据行样式
  const dataStyle = {
    font: {
      sz: 11,
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
  };
  // 创建工作簿
  const wb = utils.book_new();
  // 复制 tableDom，不影响页面显示
  const tableClone = tableDom.cloneNode(true);
  // 删除 class 为 ant-table-measure-row 的行
  const measureRows = tableClone.querySelectorAll(".ant-table-measure-row");
  measureRows.forEach(row => {
    if (row.parentNode) {
      row.parentNode.removeChild(row);
    }
  });

  // 保持百分数格式，不转换成小数
  const ws = utils.table_to_sheet(tableClone, { raw: true });
  // 设置统一行高
  const rowCount = Object.keys(ws)
    .filter(key => key[0] !== "!")
    .reduce((max, cell) => {
      const row = parseInt(cell.match(/\d+/)?.[0], 10);
      return row > max ? row : max;
    }, 0);
  ws["!rows"] = Array(rowCount + 1).fill({ hpt: 28 }); // 28磅高度

  // 设置统一列宽（比如每列20字符宽度）
  const colCount = tableClone.querySelectorAll("thead th").length;
  ws["!cols"] = Array(colCount).fill({ wch: 20 }); // 20字符宽度
  // 遍历所有单元格并应用样式
  Object.keys(ws)
    .filter(key => key[0] !== "!")
    .forEach(cell => {
      const row = parseInt(cell.match(/\d+/)?.[0], 10);
      // 应用样式
      ws[cell].s = row === 1 || (row === 2 && filter !== "计量") ? headerStyle : dataStyle;
    });

  utils.book_append_sheet(wb, ws, filter + "-" + fileName);
  // 导出文件
  writeFile(wb, `${filter}-${fileName}.xlsx`);
};

const UploadIcon = props => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
    <rect width="20" height="20" rx="4" fill="#F5F5F5" />
    <path
      d="M10 16V7M10 7L6 11M10 7L14 11"
      stroke="#746a95"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="4" y="3" width="12" height="1.5" rx="0.75" fill="#746a95" />
  </svg>
);

const DataTable = props => {
  const { tableTitle = "工单执行情况 (按设备标签)", columns, data, filter, attr } = props;
  const cDom = useRef();
  const dividerDom = useRef();
  const calculateDividers = (tableDom, dividerDom) => {
    if (!tableDom || !dividerDom) return;
    const headerRow = tableDom.querySelector("thead tr");
    const paginationDom = tableDom.querySelector(".ant-pagination");
    let pageH = 0;

    if (paginationDom) {
      const style = window.getComputedStyle(paginationDom);
      const marginTop = parseInt(style.marginTop, 10) || 0;
      const marginBottom = parseInt(style.marginBottom, 10) || 0;
      pageH = paginationDom.offsetHeight + marginTop + marginBottom;
    }
    if (!headerRow) return;
    // 清空现有分隔线
    dividerDom.innerHTML = "";
    // 获取所有表头单元格
    const ths = Array.from(headerRow.querySelectorAll("th"));
    let totalWidth = 0;

    // 计算每个分隔线位置（最后一个th之后不需要分隔线）
    for (let i = 0; i < ths.length - 1; i++) {
      const th = ths[i];
      totalWidth += th.offsetWidth;
      // 创建分隔线元素
      const divider = document.createElement("div");
      divider.className = "divider";
      divider.style.left = totalWidth + "px";
      dividerDom.appendChild(divider);
    }
    dividerDom.style.bottom = pageH + "px";
  };

  useEffect(() => {
    let timeoutId1 = null;
    let timeoutId2 = null;

    if (cDom.current && dividerDom.current) {
      timeoutId1 = setTimeout(() => {
        calculateDividers(cDom.current, dividerDom.current);
      });
    }
    const onResize = () => {
      if (cDom.current && dividerDom.current) {
        timeoutId2 = setTimeout(() => {
          calculateDividers(cDom.current, dividerDom.current);
        }, 100);
      }
    };
    window.addEventListener("resize", onResize);

    return () => {
      window.removeEventListener("resize", onResize);
      if (timeoutId1) clearTimeout(timeoutId1);
      if (timeoutId2) clearTimeout(timeoutId2);
    };
  }, [cDom, dividerDom, data, columns]);

  return (
    <>
      <div
        className="table-container-header"
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 8,
        }}
      >
        <span style={{ fontWeight: 600, fontSize: 15 }}>{tableTitle}</span>
        <span>
          <DownloadIcon
            style={{ cursor: "pointer", marginRight: 8 }}
            onClick={() => {
              downloadTable2Excel(cDom.current, filter, tableTitle);
            }}
          />
          {/* <UploadIcon style={{ cursor: "pointer" }} /> */}
        </span>
      </div>
      <div className="table-container">
        <Table
          rowKey={record => {
            return record.assetGroup;
          }}
          ref={cDom}
          {...attr}
          columns={columns}
          dataSource={data}
          scroll={{ x: 700, y: 250 }}
        ></Table>
        <div ref={dividerDom} className="divider-container" id={"dividerContainer-border"}></div>
      </div>
    </>
  );
};

const DataContainer = () => {
  const { data } = useContext(calendarContext);
  // 数据源
  const dataTypeAll = data.CalendarDataTypeAll;
  const dataTypeNursel = data.CalendarDataTypeNursel;
  const dataTypePmOrder = data.CalendarDataTypePmOrder;
  const dataTypeMeasuring = data.CalendarDataTypeMeasuring;

  const [filter, setFilter] = useState(UrlParams.getFilter());
  const [dateRange, setDateRange] = useState([UrlParams.getStartDate(), UrlParams.getEndDate()]);

  // 使用 useMemo 确保 rangeValue 与 dateRange 保持同步
  const rangeValue = useMemo(() => {
    const startDate = dateRange[0];
    const endDate = dateRange[1];

    console.log("计算 rangeValue:", { dateRange, startDate, endDate });

    return [startDate ? moment(startDate) : null, endDate ? moment(endDate) : null];
  }, [dateRange]);

  const pageDataObj = useMemo(() => {
    let percentData = null;
    let tableData = null;
    switch (filter) {
      case "all":
        percentData = [
          {
            label: "工单总数",
            value: dataTypeAll?.planSum,
            color: "#444",
            percentage: 100,
          },
          {
            label: "已完成",
            value: dataTypeAll?.finishedSum,
            color: "#5cb85c",
            percentage: 100,
          },
          {
            label: "按时完成率",
            percentage: dataTypeAll?.punctualRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.punctualRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "整体完成率",
            percentage: dataTypeAll?.finishedRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.finishedRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "初检合格率",
            percentage: dataTypeAll?.initialRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.initialRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "复检合格率",
            percentage: dataTypeAll?.retestRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.retestRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "强检合格率",
            percentage: dataTypeAll?.strongRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.strongRate)),
            color: "#5b4be1",
            sub: "%",
          },
        ];
        tableData = [
          {
            tableTitle: "工单执行情况 (按设备分类)",
            columns: dataTypeAllColumns,
            data: dataTypeAll?.assetGroupDataList,
            filter: "全部",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
          {
            tableTitle: "工单执行情况 (按设备标签分类)",
            columns: dataTypeAllColumns,
            data: dataTypeAll?.assetTagDataList,
            filter: "全部",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
        ];

        break;
      case "clinical":
        percentData = [
          { label: "工单总数", value: dataTypeNursel?.planSum, color: "#444", percentage: 100 },
          {
            label: "已完成",
            value: dataTypeNursel?.finishedSum,
            color: "#5cb85c",
            percentage: 100,
          },

          {
            label: "按时完成率",
            percentage: dataTypeNursel?.punctualRate === "/" ? 0 : toDecimal(Number(dataTypeNursel?.punctualRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "整体完成率",
            percentage: dataTypeNursel?.finishedRate === "/" ? 0 : toDecimal(Number(dataTypeNursel?.finishedRate)),
            color: "#5b4be1",
            sub: "%",
          },
        ];
        tableData = [
          {
            tableTitle: "工单执行情况 (按设备分类)",
            columns: dataTypeNurselColumns,
            data: dataTypeNursel?.assetGroupDataList,
            filter: "临床助手",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
          {
            tableTitle: "工单执行情况 (按设备标签分类)",
            columns: dataTypeNurselColumns,
            data: dataTypeNursel?.assetTagDataList,
            filter: "临床助手",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
        ];
        break;
      case "pm":
        percentData = [
          { label: "工单总数", value: dataTypePmOrder?.planSum, color: "#444", percentage: 100 },
          {
            label: "已完成",
            value: dataTypePmOrder?.finishedSum,
            color: "#5cb85c",
            percentage: 100,
          },
          {
            label: "按时完成率",
            percentage: dataTypePmOrder?.punctualRate === "/" ? 0 : toDecimal(Number(dataTypePmOrder?.punctualRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "整体完成率",
            percentage: dataTypePmOrder?.finishedRate === "/" ? 0 : toDecimal(Number(dataTypePmOrder?.finishedRate)),
            color: "#5b4be1",
            sub: "%",
          },
        ];
        tableData = [
          {
            tableTitle: "工单执行情况 (按设备分类)",
            columns: dataTypePmOrderColumns,
            data: dataTypePmOrder?.assetGroupDataList,
            filter: "预防性维护",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
          {
            tableTitle: "工单执行情况 (按设备标签分类)",
            columns: dataTypePmOrderColumns,
            data: dataTypePmOrder?.assetTagDataList,
            filter: "预防性维护",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
        ];
        break;
      case "metering":
        percentData = [
          { label: "工单总数", value: dataTypeMeasuring?.planSum, color: "#444", percentage: 100 },
          {
            label: "已完成",
            value: dataTypeMeasuring?.finishedSum,
            color: "#5cb85c",
            percentage: 100,
          },
          {
            label: "按时完成率",
            percentage:
              dataTypeMeasuring?.punctualRate === "/" ? 0 : toDecimal(Number(dataTypeMeasuring?.punctualRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "整体完成率",
            percentage:
              dataTypeMeasuring?.finishedRate === "/" ? 0 : toDecimal(Number(dataTypeMeasuring?.finishedRate)),
            color: "#5b4be1",
            sub: "%",
          },
        ];
        tableData = [
          {
            tableTitle: "强检",
            columns: dataTypeMeasuringColumns,
            data: dataTypeMeasuring?.strong,
            filter: "计量",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
          {
            tableTitle: "非强检",
            columns: dataTypeMeasuringColumns,
            data: dataTypeMeasuring?.nonstrong,
            filter: "计量",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
        ];
        break;
      default:
        percentData = [
          {
            label: "工单总数",
            value: dataTypeAll?.planSum,
            color: "#444",
            percentage: 100,
          },
          {
            label: "已完成",
            value: dataTypeAll?.finishedSum,
            color: "#5cb85c",
            percentage: 100,
          },
          {
            label: "按时完成率",
            percentage: dataTypeAll?.punctualRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.punctualRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "整体完成率",
            percentage: dataTypeAll?.finishedRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.finishedRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "初检合格率",
            percentage: dataTypeAll?.initialRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.initialRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "复检合格率",
            percentage: dataTypeAll?.retestRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.retestRate)),
            color: "#5b4be1",
            sub: "%",
          },
          {
            label: "强检合格率",
            percentage: dataTypeAll?.strongRate === "/" ? 0 : toDecimal(Number(dataTypeAll?.strongRate)),
            color: "#5b4be1",
            sub: "%",
          },
        ];
        tableData = [
          {
            tableTitle: "工单执行情况 (按设备分类)",
            columns: dataTypeAllColumns,
            data: dataTypeAll?.assetGroupDataList,
            filter: "全部",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
          {
            tableTitle: "工单执行情况 (按设备标签分类)",
            columns: dataTypeAllColumns,
            data: dataTypeAll?.assetTagDataList,
            filter: "全部",
            attr: { pagination: false, bordered: false, size: "middle" },
          },
        ];
        break;
    }
    return { percentData, tableData };
  }, [filter, dataTypeAll, dataTypeNursel, dataTypePmOrder, dataTypeMeasuring]);

  // 同步 dateRange 到 UrlParams
  useEffect(() => {
    if (dateRange[0]) {
      UrlParams.setStartDate(dateRange[0]);
    } else {
      UrlParams.setStartDate("");
    }
    if (dateRange[1]) {
      UrlParams.setEndDate(dateRange[1]);
    } else {
      UrlParams.setEndDate("");
    }
  }, [dateRange]);

  // 监听 URL 参数变化，同步 filter 和 dateRange 状态
  useEffect(() => {
    let timeoutId = null;

    const handleUrlChange = () => {
      // 添加防抖，避免频繁更新
      if (timeoutId) clearTimeout(timeoutId);

      timeoutId = setTimeout(() => {
        const newFilter = UrlParams.getFilter();
        const newStartDate = UrlParams.getStartDate();
        const newEndDate = UrlParams.getEndDate();

        console.log("DataContainer 检测到 URL 变化:", { newFilter, newStartDate, newEndDate });
        console.log("当前状态:", { filter, dateRange });

        // 同步 filter
        if (newFilter !== filter) {
          console.log("更新 filter:", newFilter);
          setFilter(newFilter);
        }

        // 同步 dateRange
        if (newStartDate !== dateRange[0] || newEndDate !== dateRange[1]) {
          console.log("更新 dateRange:", [newStartDate, newEndDate]);
          setDateRange([newStartDate, newEndDate]);
        }
      }, 50);
    };

    // 监听 popstate 事件（浏览器前进/后退按钮）
    const onPopState = () => {
      console.log("DataContainer popstate 事件触发");
      handleUrlChange();
    };

    // 监听 pushState/replaceState（程序化的 URL 变化）
    const originalPushState = window.history.pushState;
    const originalReplaceState = window.history.replaceState;

    const onPushState = function () {
      originalPushState.apply(this, arguments);
      console.log("DataContainer pushState 事件触发");
      handleUrlChange();
    };

    const onReplaceState = function () {
      originalReplaceState.apply(this, arguments);
      console.log("DataContainer replaceState 事件触发");
      handleUrlChange();
    };

    window.addEventListener("popstate", onPopState);
    window.history.pushState = onPushState;
    window.history.replaceState = onReplaceState;

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      window.removeEventListener("popstate", onPopState);
      window.history.pushState = originalPushState;
      window.history.replaceState = originalReplaceState;
    };
  }, [filter, dateRange]);

  // 组件挂载时同步 URL 参数
  useEffect(() => {
    const urlFilter = UrlParams.getFilter();
    const urlStartDate = UrlParams.getStartDate();
    const urlEndDate = UrlParams.getEndDate();

    console.log("DataContainer 初始化，同步 URL 参数:", { urlFilter, urlStartDate, urlEndDate });

    // 同步初始状态
    if (urlFilter !== filter) {
      setFilter(urlFilter);
    }

    if (urlStartDate !== dateRange[0] || urlEndDate !== dateRange[1]) {
      setDateRange([urlStartDate, urlEndDate]);
    }
  }, []); // 只在组件挂载时执行一次

  return (
    <div className="data-container-root">
      {/* 顶部过滤器 */}
      <div className="data-container-filter-row">
        <Filter value={filter} onChange={setFilter} />
        <RangePicker
          value={rangeValue}
          onChange={dates => {
            if (dates && dates[0] && dates[1]) {
              setDateRange([dates[0].format("YYYY-MM-DD"), dates[1].format("YYYY-MM-DD")]);
            } else {
              setDateRange(["", ""]);
            }
          }}
        />
      </div>
      {/* 环形百分比组件区 */}
      <div className="data-container-circle-row">
        {pageDataObj.percentData.map((item, idx) => (
          <CirclePercent key={idx} {...item} />
        ))}
      </div>
      {/* 表格区域 */}
      {pageDataObj.tableData.map((item, idx) => (
        <DataTable key={idx} {...item} />
      ))}
    </div>
  );
};

export default DataContainer;
