<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL 参数处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>QC Calendar URL 参数处理测试</h1>
    
    <div class="test-section">
        <h2>测试场景</h2>
        <p>这个测试页面用于验证修复后的 URL 参数处理是否正常工作。</p>
        
        <h3>修复的问题：</h3>
        <ul>
            <li>✅ 统一了 URL 监听机制，避免多个组件重复监听</li>
            <li>✅ 移除了 DataContainer 中不可靠的 MutationObserver</li>
            <li>✅ 简化了 Filter 组件的 URL 监听逻辑</li>
            <li>✅ 完善了 tab 切换时的参数同步</li>
        </ul>
        
        <h3>测试步骤：</h3>
        <ol>
            <li>在实际应用中打开 qc-calendar 页面</li>
            <li>切换不同的 tab（日历 ↔ 数据）</li>
            <li>在日历 tab 中切换视图模式（年视图 ↔ 月视图）</li>
            <li>修改时间范围选择器</li>
            <li>修改过滤器选项</li>
            <li>使用浏览器前进/后退按钮</li>
            <li>观察 URL 参数是否正确同步，数据是否正确加载</li>
        </ol>
        
        <h3>预期结果：</h3>
        <ul>
            <li>URL 参数应该与界面状态保持同步</li>
            <li>tab 切换时应该正确传递时间参数</li>
            <li>不应该出现重复的数据请求</li>
            <li>浏览器前进/后退应该正常工作</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>URL 参数格式示例</h2>
        <p>正确的 URL 应该包含以下参数：</p>
        <ul>
            <li><code>tab</code>: calendar 或 data</li>
            <li><code>mode</code>: year 或 month</li>
            <li><code>filter</code>: all, clinical, pm, metering</li>
            <li><code>startDate</code>: YYYY-MM-DD 格式</li>
            <li><code>endDate</code>: YYYY-MM-DD 格式</li>
            <li><code>currentDate</code>: YYYY-MM-DD 格式</li>
        </ul>
        
        <p>示例 URL：</p>
        <code>
            ?tab=data&mode=month&filter=all&startDate=2024-01-01&endDate=2024-01-31&currentDate=2024-01-15
        </code>

        <h3>测试工具：</h3>
        <button onclick="testUrlParams()">测试 URL 参数更新</button>
        <button onclick="clearConsole()">清空控制台</button>
        <div id="test-result" class="test-result" style="margin-top: 10px;"></div>
    </div>
    
    <div class="test-section">
        <h2>修复说明</h2>
        <h3>主要修改：</h3>
        <ol>
            <li><strong>App.js</strong>:
                <ul>
                    <li>增强了统一的 URL 监听机制</li>
                    <li>完善了 tab 切换时的参数同步逻辑</li>
                    <li>添加了防抖机制避免重复请求</li>
                </ul>
            </li>
            <li><strong>DataContainer.jsx</strong>:
                <ul>
                    <li>修复了 rangeValue 与 URL 参数不同步的问题</li>
                    <li>添加了完整的 URL 变化监听（包括 pushState/replaceState）</li>
                    <li>添加了组件初始化时的 URL 参数同步</li>
                    <li>使用 useMemo 优化 rangeValue 计算</li>
                </ul>
            </li>
            <li><strong>Filter.js</strong>:
                <ul>
                    <li>简化了 URL 监听，主要依赖 App.js 的统一处理</li>
                    <li>只保留必要的 popstate 监听</li>
                </ul>
            </li>
        </ol>

        <h3>RangePicker 同步修复：</h3>
        <ul>
            <li>✅ 修复了 rangeValue 计算逻辑</li>
            <li>✅ 添加了完整的 URL 变化监听机制</li>
            <li>✅ 确保 tab 切换时时间选择器正确显示</li>
            <li>✅ 支持浏览器前进/后退时的状态同步</li>
        </ul>
    </div>

    <script>
        function testUrlParams() {
            const testResult = document.getElementById('test-result');
            const currentUrl = new URL(window.location);

            // 测试不同的日期参数
            const testCases = [
                { startDate: '2024-01-01', endDate: '2024-01-31', tab: 'data' },
                { startDate: '2024-02-01', endDate: '2024-02-29', tab: 'data' },
                { startDate: '2024-03-01', endDate: '2024-03-31', tab: 'calendar' },
            ];

            let currentTest = 0;

            function runNextTest() {
                if (currentTest >= testCases.length) {
                    testResult.innerHTML = '<div class="success">所有测试完成！请检查控制台输出。</div>';
                    return;
                }

                const testCase = testCases[currentTest];
                const newUrl = new URL(window.location);

                Object.keys(testCase).forEach(key => {
                    newUrl.searchParams.set(key, testCase[key]);
                });

                testResult.innerHTML = `<div>正在测试: ${JSON.stringify(testCase)}</div>`;

                // 更新 URL
                window.history.pushState(null, '', newUrl.toString());

                currentTest++;
                setTimeout(runNextTest, 2000); // 2秒后运行下一个测试
            }

            console.log('开始 URL 参数测试...');
            runNextTest();
        }

        function clearConsole() {
            console.clear();
            document.getElementById('test-result').innerHTML = '';
        }
    </script>
</body>
</html>
