import { rest, urls, sha1, storage } from '../constants'
import wx from 'weixin-js-sdk'
import * as dd from 'dingtalk-jsapi'
import GELogo from '@/images/gehealthcare/GEHC-small.png'
import { browserHistory } from 'react-router'

/**
 * 微信, 钉钉以及GEAPM的app(flutter)都采用js api的方式与前端网页通信
 * 这个文件将会是统一外边js api的入口
 */

const jsApiList = {
  wx: [
    'miniProgram',
    'onMenuShareAppMessage',
    'updateAppMessageShareData',
    'onMenuShareTimeline',
    'updateTimelineShareData',
    'getLocalImgData',
    'chooseImage',
    'uploadImage',
    'scanQRCode',
    'startRecord',
    'stopRecord',
    'playVoice',
    'uploadVoice',
    'stopVoice',
    'onVoiceRecordEnd',
    'onVoicePlayEnd',
    'getLocation',
    'downloadFile'
  ],
  dd: [
    'biz.util.uploadImage',
    'device.audio.startRecord',
    'device.audio.stopRecord',
    'device.audio.onRecordEnd',
    'device.audio.download',
    'device.audio.play',
    'device.audio.stop',
    'device.audio.onPlayEnd'
  ],
  tt: ['scanCode', 'chooseImage', 'getFileSystemManager', 'getRecorderManager', 'createInnerAudioContext']
}
export const inIframe = () => top.location !== location

export const isFeiShu = () => {
  return /Feishu/i.test(navigator.userAgent) || /Lark/i.test(navigator.userAgent)
}

// 判断是否是企业微信环境
export const isWxWork = () => {
  return /wxwork/i.test(navigator.userAgent);
}

export const isWxBrowser = () => {
  return /MicroMessenger/i.test(navigator.userAgent) && !/wechatdevtools/i.test(navigator.userAgent)
}

export const isMiniProgame = () => {
  return /miniProgram/i.test(navigator.userAgent)
}

export const isAndroid = () => navigator.userAgent.toLowerCase().indexOf('android') > -1

export const isIOS = () => /iPad|iPhone|iPod/.test(navigator.userAgent)

export const isDDBrowser = () => {
  try {
    if (dd && dd.env && dd.env.platform === 'notInDingTalk') {
      return false
    } else {
      return true
    }
  } catch (err) {
    return false
  }
}

export const isAPMBrowser = () => {
  if (window.gfc && window.gfc.audio) {
    return window.gfc
  } else {
    return null
  }
}

export const isAppOffline = () => {
  if (window.gfc && window.gfc.networkStatus() === 'offline') {
    return true
  } else {
    return false
  }
}

export async function jsApiInit() {
  const env = {}
  if (isAPMBrowser()) {
    env.gfc = isAPMBrowser()
  }
  if (isWxBrowser()) {
    env.wx = await wxJsApiInit()
  }
  if (isDDBrowser()) {
    env.dd = await ddJsApiInit()
  }
  if (isFeiShu()) {
    env.tt = await fsJsApiInit()
  }
  return env
}

export const setDDTicket = res => {
  const time = new Date().getTime()
  localStorage.setItem(storage.ddCorpId, res.corpId)
  localStorage.setItem(storage.ddAgentId, res.agentId)
  localStorage.setItem(storage.ticket, res.ticket)
  res.expires_in && localStorage.setItem(storage.ticketExpire, time + res.expires_in)
}

export const getDDTicket = async () => {
  const time = new Date().getTime()
  const ddApiTicket = {
    agentId: localStorage.getItem(storage.ddAgentId),
    corpId: localStorage.getItem(storage.ddCorpId),
    ticket: localStorage.getItem(storage.ticket),
    expires_in: localStorage.getItem(storage.ticketExpire)
  }
  if (ddApiTicket.corpId && ddApiTicket.expires_in && time < parseInt(ddApiTicket.expires_in)) {
    return ddApiTicket
  } else {
    const res = await fetch(urls.ddApiTicket)
    const data = res.json()
    if (data && data.corpId) {
      setDDTicket(data)
    }
    return data
  }
}

console.log('sessionStorage path4wxsdk', sessionStorage.getItem('hc-apm-path4wxsdk'))
// 初始化执行一次
let cached = false // cache wx
let path4wxsdk = window.location.href.split('#')[0]
// let path4wxsdk = sessionStorage.getItem('hc-apm-path4wxsdk') || window.location.href.split('#')[0]
// sessionStorage.setItem('hc-apm-path4wxsdk', path4wxsdk)

let fsApiTicketRes = null

export const getFsTicket = async () => {
  return new Promise(
    async (resolve, reject) => {
      if (!fsApiTicketRes) {
        fsApiTicketRes = await rest.get(urls.fsApiTicket)
        console.log('getFsTicket', fsApiTicketRes)
      }
      resolve({
        appId: fsApiTicketRes.appId,
        jsapi_ticket: fsApiTicketRes.ticket
      })
      // // 创建 <script> 元素
      // var script = document.createElement("script");

      // // 设置 <script> 元素的属性
      // script.type = "text/javascript";
      // script.src = "https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.23.js";
      // // 异步加载脚本文件
      // script.onload = function () {
      //   // 加载成功后的回调函数
      //   resolve({
      //     appId: fsApiTicketRes.appId,
      //     jsapi_ticket: fsApiTicketRes.ticket
      //   })
      // };

      // // 将 <script> 元素添加到页面中
      // document.head.appendChild(script);
    },
    err => {}
  )
}

const fsJsApiInit = async () => {
  console.log('fsJsApiInit')
  const { jsapi_ticket, appId } = await getFsTicket()
  console.log('fsJsApiInit result', { jsapi_ticket, appId })
  return new Promise((resolve, reject) => {
    let cfg = {
      debug: true,
      appId,
      nonceStr: (Math.random() + 1).toString(36).substring(7),
      url: window.location.href.split('#')[0],
      jsApiList: jsApiList.tt,
      jsapi_ticket,
      locale: 'zh-CN',
      timestamp: Date.now()
    }

    cfg.signature = sha1.hex(
      `jsapi_ticket=${cfg.jsapi_ticket}&noncestr=${cfg.nonceStr}&timestamp=${cfg.timestamp}&url=${cfg.url}`
    )
    cfg.onSuccess = res => {
      console.log('fsJsApiInit onSuccess', res)
      cached = window.tt
      resolve(window.tt)
    }
    cfg.onFail = err => {
      console.log('fsJsApiInit onFail', JSON.stringify(err))
      reject(`config failed: ${JSON.stringify(err)}`)
    }
    console.log('fs jsapi cfg', cfg, window.h5sdk)
    window.h5sdk.config(cfg)
    // 通过error接口处理API验证失败后的回调
    window.h5sdk.error(err => {
      throw ('h5sdk error:', JSON.stringify(err))
    })
    window.h5sdk.ready(() => {
      console.log('feishu h5sdk ready...')
    })
  })
}

const ddJsApiInit = async () => {
  console.log('ddJsApiInit')
  const { ticket: jsapi_ticket, agentId, corpId } = await getDDTicket()
  console.log('ddJsApiInit result', { jsapi_ticket, agentId, corpId })
  return new Promise((resolve, reject) => {
    let cfg = {
      agentId,
      corpId,
      nonceStr: (Math.random() + 1).toString(36).substring(7),
      url: window.location.href.split('#')[0],
      jsApiList: jsApiList.dd,
      jsapi_ticket,
      type: 0,
      timeStamp: Math.floor(Date.now() / 1000)
    }

    cfg.signature = sha1.hex(
      `jsapi_ticket=${cfg.jsapi_ticket}&noncestr=${cfg.nonceStr}&timestamp=${cfg.timeStamp}&url=${cfg.url}`
    )
    dd.config(cfg)
    dd.error(err => {
      console.error('ddJsApiInit', err)
      reject(err)
    })
    dd.ready(() => {
      console.log('ddJsApiInit success')
      resolve(dd)
    })
  })
}

export const wxJsApiInit = () => {
  return new Promise((resolve, reject) => {
    let jsapi_ticket = localStorage.getItem(storage.ticket)
    if (isIOS() && cached) {
      resolve(wx)
    } else if (isAndroid() && cached && path4wxsdk === window.location.href.split('#')[0]) {
      resolve(wx)
    } else {
      let cfg = {
        debug: false,
        appId: localStorage.getItem(storage.appId),
        nonceStr: (Math.random() + 1).toString(36).substring(7),
        url: isIOS() ? path4wxsdk : window.location.href.split('#')[0],
        jsApiList: jsApiList.wx,
        timestamp: Math.floor(Date.now() / 1000),
        jsapi_ticket
      }
      const wxRegister = () => {
        cfg.signature = sha1.hex(
          `jsapi_ticket=${cfg.jsapi_ticket}&noncestr=${cfg.nonceStr}&timestamp=${cfg.timestamp}&url=${cfg.url}`
        )
        wx.config(cfg)
        wx.ready(() => {
          console.log('微信网页验权成功...', cfg, location)
          console.log('path4wxsdk: ', path4wxsdk)
          cached = wx
          isAndroid() && (path4wxsdk = window.location.href.split('#')[0])
          setShareInfo()
          resolve(wx)
        })
        wx.error(res => {
          console.log('微信验权失败... path4wxsdk: ', path4wxsdk)
          console.warn(res, cfg, location)
          reject(res)
        })
      }
      if (!cfg.appId || !cfg.jsapi_ticket) {
        rest.get(urls.unauthorizedWeChateInfo).then(res => {
          if (res) {
            cfg.appId = res.appId
            cfg.jsapi_ticket = res.jsApiToken
            wxRegister()
          }
        })
      } else {
        wxRegister()
      }
    }
  })
}

export function saveWeChatInfo(weChatInfo) {
  if (weChatInfo && weChatInfo.appId) {
    localStorage.setItem(storage.appId, weChatInfo.appId)
    localStorage.setItem(storage.wxToken, weChatInfo.token)
    localStorage.setItem(storage.ticket, weChatInfo.jsApiToken)
  } else if (isWxBrowser()) {
    rest.get(urls.unauthorizedWeChateInfo).then(saveWeChatInfo)
  }
}

const defaultCfg = () => {
  return {
    title: document.title,
    link: location.href.split('#')[0],
    desc: '用户分享',
    imgUrl: GELogo,
    success: () => {
      console.log('微信分享defaultCfg', location.href)
    }
  }
}

const setOgProp = (key, value) => {
  const og = document.querySelector(`meta[property=${key}]`)
  og.setAttribute('content', value)
}

// 监听浏览器history state变化用于恢复默认设置
window.pushTime = 0
let shareCfgLocation = null
const isDiffRoute = (a, b) => a.key !== b.key || a.pathname !== b.pathname
browserHistory.listen(route => {
  console.log('history listen: ', shareCfgLocation, route)
  if (shareCfgLocation && isDiffRoute(route, shareCfgLocation)) {
    setShareInfo()
    shareCfgLocation = null
  }
  if (isIOS() && inIframe() && ['PUSH'].includes(route.action)) {
    window.parent.postMessage(
      {
        type: 'iframeRouteChange',
        payload: { action: route.action, pathname: route.pathname }
      },
      '*'
    )
  }

  // 监听变化向App发消息
  const { gfc, historyChange, gfcPopstate } = window
  const { action } = route
  const { fromMsg } = browserHistory.getCurrentLocation().query

  if (gfc && (['PUSH', 'REPLACE'].includes(route.action) || (action === 'POP' && !fromMsg))) {
    historyChange && historyChange.postMessage(action)
  }

  if (gfc && fromMsg && action === 'POP') {
    gfcPopstate.postMessage('POP')
  }
})
// 监听IOS浏览器从外部返回
window.addEventListener('pageshow', event => {
  if (event.persisted && isIOS()) {
    console.log('IOS浏览器从外部返回，重新执行wxJsApiInit')
    cached = false
    path4wxsdk = window.location.href.split('#')[0]
    isWxBrowser() && wxJsApiInit().then()
  }
})
// 监听浏览器返回动作
window.addEventListener('popstate', event => {
  const sendMsg = () => {
    const iframe = document.getElementsByTagName('iframe')[0]

    if (iframe && isIOS()) {
      let getpushTime = browserHistory.getCurrentLocation().hash.split('#')[1]
      if (getpushTime > pushTime) {
        pushTime = getpushTime
        iframe.contentWindow.postMessage(
          {
            type: 'parentRouteChange',
            payload: { action: 'PUSH' }
          },
          '*'
        )
      } else {
        pushTime = getpushTime
        iframe.contentWindow.postMessage(
          {
            type: 'parentRouteChange',
            payload: { action: 'POP' }
          },
          '*'
        )
      }
    }
  }
  setTimeout(sendMsg)
  console.log('popstate shareCfgLocation', browserHistory.getCurrentLocation(), shareCfgLocation)
  if (shareCfgLocation && isDiffRoute(browserHistory.getCurrentLocation(), shareCfgLocation)) {
    setShareInfo()
    shareCfgLocation = null
  }
  const { query } = browserHistory.getCurrentLocation()
  if (isWxBrowser() && query.code) {
    setTimeout(() => {
      console.log('关闭微信浏览器', window.WeixinJSBridge)
      window.WeixinJSBridge && window.WeixinJSBridge.call('closeWindow')
    }, 100)
  }
})

// 监听窗口消息用于iframe中进行的微信分享设置
window.addEventListener('message', event => {
  if (event.data && event.data.type === 'setShareInfo') {
    console.log('收到窗口消息setShareInfo', event)
    setShareInfo(event.data.payload)
  }
  // if (event.data && event.data.type === 'iframeRouteChange') {
  //   console.log('收到iframe窗口消息iframeRouteChange', event)
  //   if (event.data.payload.action === 'PUSH') {
  //     history.pushState({}, document.title, `#${new Date().getTime()}`)
  //     pushTime = new Date().getTime()
  //   }
  // }
  if (event.data && event.data.type === 'parentRouteChange') {
    console.log('自身iframe窗口消息parentRouteChange', event)
    if (event.data.payload.action === 'POP') {
      browserHistory.go(-1)
    }
    if (event.data.payload.action === 'PUSH') {
      browserHistory.go(1)
    }
  }
})

export const setShareInfo = config => {
  document.title = (config && config.title) || '资产智管家'
  if (config) {
    shareCfgLocation = browserHistory.getCurrentLocation()
  }
  const newConfig = {
    ...defaultCfg(),
    ...config,
    success: () => {
      console.log('setShareInfo分享回调成功', newConfig)
    }
  }
  if (typeof newConfig.imgUrl === 'string' && newConfig.imgUrl.indexOf('/') === 0) {
    newConfig.imgUrl = location.origin + newConfig.imgUrl
  }
  console.log(config ? '此页面有单独的分享配置: ' : '恢复默认分享', newConfig, shareCfgLocation)

  const update = () => {
    jsApiInit().then(({ wx, dd }) => {
      if (wx) {
        wx.onMenuShareAppMessage(newConfig)
        wx.onMenuShareTimeline(newConfig)
        wx.updateAppMessageShareData(newConfig)
        wx.updateTimelineShareData(newConfig)
      } else if (dd) {
        dd.biz.util.share({
          type: 0, //分享类型，0:全部组件 默认；1:只能分享到钉钉；2:不能分享，只有刷新按钮
          url: newConfig.link,
          title: newConfig.title,
          content: newConfig.desc,
          image: newConfig.imgUrl,
          onSuccess: function () {
            console.log('钉钉分享成功')
          },
          onFail: function (err) {
            console.log('钉钉分享失败', err)
          }
        })
      } else {
        console.log('设置网页分享', newConfig)
        // 为普通html转发提供描述
        newConfig.title && setOgProp('og\\:title', newConfig.title)
        newConfig.imgUrl && setOgProp('og\\:image', newConfig.imgUrl)
        newConfig.desc && setOgProp('og\\:description', newConfig.desc)
      }
    })
  }

  if (config) {
    setTimeout(update, 1000)
  } else {
    update()
  }
}
