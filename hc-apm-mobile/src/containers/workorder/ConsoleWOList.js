import React from 'react'
import { browserHistory as history } from 'react-router'
import { APMList as List, Select, SelectOrgNew } from '../../components'
import { route_urls, urls } from '../../constants'
import { Form } from 'react-weui'
import { badgeStyle } from './ConsoleSRList'
import { connect } from 'dva'
import { Form as APMForm } from '@components'

class ConsoleWOList extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      from:
        (props.location.query && props.location.query.from) ||
        (props.location.state && props.location.state.from) ||
        'repairList',
      request: {
        title: '工单列表',
        url: (props.location.state && props.location.state.url) || urls.woPickedup,
        query: (props.location.state && props.location.state.cons) || props.location.query,
        itemHeader: { key: 'serviceRequest.srNum', label: '报修编号' },
        itemContent: this.getItemContent(props),
        badge: { key: 'serviceRequest.casePriority', label: '紧急程度', type: 'casePriority' },
        badgeStyle
      }
    }
    this.eventClick = this.eventClick.bind(this)
  }

  getItemContent(props) {
    var status =
      (props.location.query && props.location.query.status) != null
        ? props.location.query.status
        : props.location.state && props.location.state.cons && props.location.state.cons.status
    return status == 4 || status == 5
      ? [
        { key: 'assetInfo.financingNum', label: '资产编号' },
        { key: 'assetInfo.name', label: '资产名称' },
        { key: 'assetInfo.functionType', label: '型号' },
        { key: 'assetInfo.clinicalDeptName', label: '所属科室' },
        { key: 'serviceRequest.requestorName', label: '报修人' },
        { key: 'serviceRequest.requestTime', label: '报修时间' },
        { key: 'intExtType', label: '工单类型', type: 'intExtType' },
        { key: 'serviceRequest.requestReason', label: '故障描述' },
        { key: 'serviceRequest.repairReportSendPlace', i18n: 'repairReportSendPlace', type: 'repairReportSendPlace' },
        // { key: 'currentStepId', label: '当前步骤', type: 'ProgressBar' }
        { key: 'currentStepId', label: '当前步骤', type: 'woSteps' }
      ]
      : [
        { key: 'assetInfo.financingNum', label: '资产编号' },
        { key: 'assetInfo.name', label: '资产名称' },
        { key: 'assetInfo.functionType', label: '型号' },
        { key: 'assetInfo.clinicalDeptName', label: '所属科室' },
        { key: 'serviceRequest.requestorName', label: '报修人' },
        { key: 'serviceRequest.requestTime', label: '报修时间' },
        { key: 'closeTime', label: '完成时间' },
        { key: 'currentPersonName', label: '当前负责人' },
        { key: 'intExtType', label: '工单类型', type: 'intExtType' },
        { key: 'serviceRequest.requestReason', label: '故障描述' },
        { key: 'serviceRequest.repairReportSendPlace', i18n: 'repairReportSendPlace', type: 'repairReportSendPlace' },
        // { key: 'currentStepId', label: '当前步骤', type: 'ProgressBar' }
        { key: 'currentStepId', label: '当前步骤', type: 'woSteps' }
      ]
  }

  eventClick(wo) {
    history.push({ pathname: route_urls.wx.workorder + '/' + wo.id, query: { from: this.state.from } }) //assethead or dispatcher
  }

  eventChangeQuery = e => {
    this.state.request.query = { ...this.state.request.query, ...e }
    this.setState(this.state)
  }

  hideHospitalDistrictSubOrg(item) {
    return item.orgType < 4
  }

  justHospitalDistrictEnable(item) {
    return ![3].includes(item.orgType)
  }

  renderFilter = () => {
    var status =
      (this.props.location.query && this.props.location.query.status) != null
        ? this.props.location.query.status
        : this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status
    if (status == 2) {
      return (
        <APMForm
          value={{}}
          groups={[
            {
              items: [
                {
                  key: 'clinicalUid',
                  label: '所属科室',
                  component: 'SelectOrgNew',
                  transform: item => item.clinicalDeptUID,
                  props: { disableOrg: org => org.orgType !== 4 }
                },
                {
                  key: 'closeTime',
                  label: '关单日期',
                  component: 'Date'
                }
              ]
            }
          ]}
          onChange={this.eventChangeQuery}
        />
      )
    }
  }

  render() {
    return (
      <div>
        {((this.props.location.query && this.props.location.query.status) != null
          ? this.props.location.query.status
          : this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status) ==
          4 &&
          ((this.props.location.query && this.props.location.query.from) != null
            ? this.props.location.query.from
            : this.props.location.state && this.props.location.state.from) == 'workorderList' && (
          <Form>
            <SelectOrgNew
              label={'所属院区'}
              style={{ height: '2.8rem', lineHeight: '2.8rem' }}
              drawerHeight="80" //单位为vh
              // warn={true}
              // defaultValue={}
              // org={}
              showOrg={this.hideHospitalDistrictSubOrg}
              disableOrg={this.justHospitalDistrictEnable}
              onChange={v => this.eventChangeQuery({ siteUid: v.siteUID })}
            />

            <Select
              item={{
                key: 'stepId',
                label: '工单状态',
                options: [
                  { key: 3, value: '未接单' },
                  { key: 4, value: '维修中' },
                  { key: 5, value: '待验收' },
                  { key: 6, value: '待关单' }
                ]
              }}
              onChange={e => this.eventChangeQuery({ stepId: e })}
            />
          </Form>
        )}
        {this.renderFilter()}
        <List {...this.state.request} onClick={this.eventClick} />
      </div>
    )
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself
  }
}

export default connect(mapStateToProps)(ConsoleWOList)
