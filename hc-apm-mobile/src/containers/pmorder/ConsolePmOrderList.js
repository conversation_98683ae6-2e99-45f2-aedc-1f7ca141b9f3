import React from 'react'
import { connect } from 'react-redux'
import { browserHistory as history } from 'react-router'
import { Form, FormCell, CellHeader, Label, CellBody, Input, CellFooter, Icon, Dialog, Switch } from 'react-weui'
import { List, SelectOrgNew, Select, Form as GeneralForm, sign } from '../../components'
import { route_urls, rest, urls, util } from '../../constants'
import CancelPmOrders from './CancelPmOrders.js'
import { Button, message } from 'antd'
import UrlPattern from 'url-pattern'
import { SearchBar } from 'antd-mobile'
import moment from 'moment'
import { isAppOffline } from '@/actions/jsApi'

const LEVEL = {
  0: 'tenantUID',
  1: 'institutionUID',
  2: 'hospitalUID',
  3: 'siteUID'
}
class ConsolePmOrderList extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      from: 'pmList',
      loading: false,
      hasMore: false,
      org: null,
      page: 0,
      url: (props.location.state && props.location.state.url) || urls.pmOrderPickedUp,
      query: (props.location.state && props.location.state.cons) || props.location.query,
      request: {
        title: '工单列表',
        // url: props.location.state.url,
        // query: props.location.state.cons,
        data: [],
        count: 0,
        selectable: record => (((props.location.query && props.location.query.batch) != null ? props.location.query.batch : (props.location.state && props.location.state.cons && props.location.state.cons.batch))) == 0 && record.approvalStatus != 1,
        onSelect: selected => this.setState({ selectedItems: selected }),
        itemHeader: { key: 'pmNum', label: '保养编号' },
        itemContent: this.getItemContent(props)
      },
      requestAcceptance: {
        selectable: record => [1, 2].includes(record.ruleLevel)
      },
      queryParam: Object.assign({
        ruleLevel: null,
        clinicalId: null,
        planTime: null,
        startPlanCheck: false,
        emergencyEquipment: false,
        assetName: ''
      }, props.location.state && props.location.state.queryParam),
      selectedItems: [],
      isAllOperation: false,
      showDialog: false,
      showAcceptanceDialog: false,
      form: {
        manHours: 0.1,
        endTime: ''
      },
      acceptanceForm: {
        remarks: ''
      },
      isListVisible: true
    }
    this.eventClick = this.eventClick.bind(this)
    this.eventOnMore = this.eventOnMore.bind(this)
  }

  componentWillMount() {
    const { myself } = this.props
    const orgUID = myself.userAccount[LEVEL[myself.userAccount.orgLevel]]
    if (orgUID) {
      rest.get(urls.org + '/' + orgUID).then(org => this.setState({ org }))
      this.pmPickedup()
    }
  }

  searchSubmit(keyword) {
    let queryParam = this.state.queryParam
    queryParam.assetName = keyword
    this.setState({ queryParam }, () => {
      this.pmPickedup()
    })
  }

  getItemContent(props) {
    return [
      { key: 'assetName', label: '资产名称' },
      { key: 'assetInfo.departNum', label: '设备编号' },
      { key: 'assetInfo.functionType', label: '设备型号' },
      { key: 'assetInfo.serialNum', label: '序列号' },
      { key: 'assetInfo.manufacture', label: '厂商' },
      { key: 'planTime', label: '计划时间' },
      { key: 'endTime', label: '完成时间' },
      { key: 'currentStepName', label: '当前步骤', type: 'woSteps' },
      { key: 'ruleLevel', label: '保养等级', type: 'ruleLevel' },
      { key: 'ruleName', label: '保养规则' },
      { key: 'assetInfo.clinicalDeptName', label: '所属科室' },
      { key: 'assetInfo.emergencyEquipment', label: '急救设备', type: 'emergencyEquipment' }
    ]
  }

  pmPickedup() {
    this.setState({ loading: true, page: 0 })
    rest.list(this.state.url, { ...this.state.queryParam, page: 0, ...this.state.query }).then(res => {
      // debugger
      this.state.request.data = []
      this.state.request.count = 0
      this.state.hasMore = res.data.length == 10
      this.state.loading = false
      this.state.isListVisible = false
      this.setState(this.state, () => {
        this.state.request.data = res.data
        this.state.request.count = res.rowCount
        this.state.selectedItems = []
        this.state.isAllOperation = false
        this.state.isListVisible = true
        this.setState(this.state)
      })
    })
  }

  eventClick(pm) {
    const { queryParam } = this.state
    let id = pm.id
    history.push({
      pathname: new UrlPattern(route_urls.wx.pmorder).stringify({ id }),
      query: { from: this.state.from },
      state: {
        ...this.props.location.state,
        pm,
        queryParam
      }
    }) //assethead or dispatcher
  }

  eventOnMore() {
    this.state.page++
    this.setState(this.state, () => this.loadMoreData())
  }

  loadMoreData() {
    this.setState({ loading: true })
    rest.list(this.state.url, { ...this.state.queryParam, page: this.state.page, ...this.state.query }).then(res => {
      this.state.request.data = this.state.request.data.concat(res.data)
      this.state.hasMore = res.data.length == 10
      this.state.loading = false
      this.setState(this.state)
    })
  }

  eventChangeQuery(e) {
    this.state.queryParam = { ...this.state.queryParam, ...e }
    this.setState(this.state, () => this.pmPickedup())
  }

  disableOrg(item) {
    return ![3, 4].includes(item.orgType) // 只能选院区或科室
  }

  onOrgSelect = e => {
    if (e.siteUID) {
      this.eventChangeQuery({ siteUID: e.siteUID, clinicalId: null })
    } else if (e.clinicalDeptUID) {
      this.eventChangeQuery({ siteUID: null, clinicalId: e.clinicalDeptUID })
    } else {
      this.eventChangeQuery({ siteUID: null, clinicalId: null })
    }
  }

  deletePlanTime() {
    this.eventChangeQuery({ planTime: '' })
  }

  renderButtons() {
    const { workflowConfig } = this.props
    const { selectedItems } = this.state
    const allRuleLevel2 = selectedItems.every(item => [1, 2].includes(item.ruleLevel))
    const showBatchFinish = workflowConfig && workflowConfig.pmIsSupportBatchFinish !== false && allRuleLevel2
    return (
      <div style={{ width: '100vw', display: 'flex', justifyContent: 'space-around', position: 'fixed', bottom: 10 }}>
        {showBatchFinish && (
          <Button type="normal" icon="check-circle-o" onClick={() => this.submitAll()}>
            全部完成
          </Button>
        )}
        {showBatchFinish && (
          <Button type="primary" icon="exclamation-circle-o" onClick={() => this.submitPart()}>
            批量完成
          </Button>
        )}
        <CancelPmOrders items={this.state.selectedItems} />
      </div>
    )
  }

  renderAcceptanceButtons() {
    const status = this.state.query.status == 6
    return (
      <div style={{ width: '100vw', display: 'flex', justifyContent: 'space-around', position: 'fixed', bottom: 10 }}>
        {status && (
          <Button type="" icon="exclamation-circle-o" onClick={() => this.submitAllAcceptOrNot()}>
            全部验收
          </Button>
        )}
        {status && (
          <Button type="primary" icon="exclamation-circle-o" onClick={() => this.submitAcceptOrNot()}>
            批量验收
          </Button>
        )}
      </div>
    )
  }

  submitAll() {
    this.setState({ showDialog: true, isAllOperation: true })
  }

  submitPart() {
    if (this.state.selectedItems.length == 0) {
      message.info('没有需要完成的二级保养工单', 2)
      return
    }
    this.setState({ showDialog: true, isAllOperation: false })
  }

  submit(val) {
    this.setState({ showDialog: false })
    val = { ...this.state.form, ...val }
    if (this.state.isAllOperation) {
      this.submitAllFinished(val)
    } else {
      this.submitPartFinished(val)
    }
  }

  submitPartFinished(val) {
    let selected = this.state.selectedItems
    const avgManHours = Math.round((val.manHours * 100) / selected.length) / 100
    let result = selected.map(item => ({
      manHours: avgManHours,
      endTime: val.endTime,
      actionType: 'repair',
      planTime: item.planTime,
      qualityChecking: 1,
      stepId: 'Maintain',
      pmOrderId: item.id,
      wcSignature: val.objectId,
      comments: val.comments,
      assignee: val.assignee
    }))
    rest
      .put(urls.pmOrderBatch, result, true, { extraHeaders: { taskSource: 'pmorderBatchComplete' } })
      .then(() => util.reload())
      .catch(err => {
        message.error('提交失败', err.message)
        // history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
      })
  }

  submitAllFinished(val) {
    let result = {
      ruleLevel: parseInt(this.state.queryParam.ruleLevel),
      assignee: val.assignee,
      comments: val.comments,
      endTime: val.endTime,
      actionType: 'repair',
      manHours: Math.round(val.manHours * 100) / 100,
      qualityChecking: 1,
      stepId: 'Maintain',
      wcSignature: val.objectId
    }

    const searchParams = this.paramsGetType(this.state.queryParam)
    rest
      .put(`${urls.pmOrderAllDone}?${searchParams}`, result, true, {
        extraHeaders: { taskSource: 'pmorderAllComplete' }
      })
      .then(() => util.reload())
      .catch(err => {
        message.error('全部完成提交失败', err.message)
      })
  }

  submitWithSignature(val) {
    this.setState({ showDialog: false })
    const { workflowConfig } = this.props
    if (workflowConfig.pmWcNeedSign) {
      sign('请签名后完成保养', objectId => {
        val = { objectId, ...val }
        this.submit(val)
      })
    } else {
      this.submit(val)
    }
  }

  submitAllAcceptOrNot() {
    this.setState({ showAcceptanceDialog: true, isAllOperation: true })
  }

  submitAcceptOrNot() {
    if (this.state.selectedItems.length == 0) {
      message.info('没有需要完成的二级保养工单', 2)
      return
    }
    this.setState({ showAcceptanceDialog: true, isAllOperation: false })
  }

  acceptancePassWithSignature = async (val) => {
    this.setState({ showAcceptanceDialog: false })
    const { ruleLevel, clinicalId, siteUID } = this.state.queryParam
    let res
    if (this.state.isAllOperation) {
      res = await rest.post(urls.pmAckNeedSign, { ruleLevel, clinicalId, siteUID })
    } else if (Array.isArray(this.state.selectedItems) && this.state.selectedItems.length > 0) {
      const assetUid = this.state.selectedItems.map(item => item.assetUid)
      res = await rest.post(urls.pmAckNeedSign, { assetUid })
    }
    if (res) {  // res: true 需要签名
      sign('请签名后完成验收', objectId => {
        val = { objectId, ...val }
        this.acceptancePass(val)
      })
    } else {
      this.acceptancePass(val)
    }
  }

  acceptancePass(val) {
    this.setState({ showAcceptanceDialog: false })
    val = { ...this.state.acceptanceForm, ...val }

    if (this.state.isAllOperation) {
      const searchParams = this.paramsGetType(this.state.queryParam)
      let result = {
        actionType: 'acceptancePass',
        stepId: 'Acceptance',
        remarks: val.remarks,
        ackSignature: val.objectId
      }

      rest
        .put(`${urls.pmOrderAllDone}?${searchParams}`, result)
        .then(() => util.reload())
        .catch(err => {
          message.error('全部验收通过提交失败', err.message)
        })
    } else {
      let selected = this.state.selectedItems
      let result = selected.map(item => ({
        actionType: 'acceptancePass',
        stepId: item.currentStepId,
        pmOrderId: item.id,
        remarks: val.remarks,
        ackSignature: val.objectId
      }))

      rest
        .put(urls.pmOrderBatch, result)
        .then(() => util.reload())
        .catch(err => {
          message.error('提交失败', err.message)
          // history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
        })
    }
  }

  acceptanceReject(val) {
    this.setState({ showAcceptanceDialog: false })
    val = { ...this.state.acceptanceForm, ...val }
    // console.log('acceptanceReject' + val.remarks)

    if (this.state.isAllOperation) {
      let result = {
        actionType: 'acceptanceReject',
        stepId: 'Acceptance',
        remarks: val.remarks
      }

      rest
        .put(urls.pmOrderAllDone, result)
        .then(() => util.reload())
        .catch(err => {
          message.error('全部验收不通过提交失败', err.message)
        })
    } else {
      let selected = this.state.selectedItems
      let result = selected.map(item => ({
        actionType: 'acceptanceReject',
        stepId: item.currentStepId,
        pmOrderId: item.id,
        remarks: val.remarks
      }))

      rest
        .put(urls.pmOrderBatch, result)
        .then(() => util.reload())
        .catch(err => {
          message.error('提交失败', err.message)
          // history.replace({ pathname: route_urls.msg, query: { code: err.code }, state: { desc: err.message } })
        })
    }
  }

  paramsGetType(result) {
    const params = Object.keys(result)
      .filter(key => result[key] !== null)
      .map(key => `${key}=${result[key]}`)
      .join('&')
    return params
  }

  generateForm() {
    const { workflowConfig } = this.props
    let autoAck = workflowConfig.autoAckMaintenance == null ? true : workflowConfig.autoAckMaintenance

    let selected = this.state.selectedItems
    let pmOrderIds = []
    if (selected && selected.length > 0) {
      selected.map(select => {
        pmOrderIds.push(select.id)
      })
    }

    return {
      buttons: [
        { type: 'primary', label: '完成', validation: true, onClick: val => this.submitWithSignature(val) },
        {
          type: 'primary',
          label: '取消',
          onClick: val => {
            this.setState({ showDialog: false })
          }
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'manHours',
              label: '工时(小时)',
              component: 'Number',
              rule: { required: true, max: 999, min: 0 }
            },
            {
              key: 'endTime',
              label: '结束保养时间',
              component: 'Date',
              rule: { required: true }
            },
            {
              key: 'assignee',
              label: '验收人',
              component: 'SelectUser',
              rule: { required: !autoAck },
              props: {
                posturl: !this.state.isAllOperation
                  ? new UrlPattern(`${urls.pmOrder}/batch/acker_with_pm_ids`).stringify({})
                  : '',
                url: this.state.isAllOperation
                  ? `${urls.getPmAcceptor}?${this.paramsGetType(this.state.queryParam)}`
                  : '',
                first: false,
                show: false,
                databody: pmOrderIds
              },
              transform: user => user.uid,
              query: {},
              hide: v => autoAck
            },
            {
              key: 'comments',
              label: '工单备注',
              component: 'TextArea',
              props: { placeholder: '工单备注' }
            },
          ]
        }
      ]
    }
  }

  generateAcceptanceForm() {
    return {
      buttons: [
        { type: 'primary', label: '通过', validation: true, onClick: this.acceptancePassWithSignature },
        { type: 'primary', label: '不通过', validation: true, onClick: val => this.acceptanceReject(val) },
        {
          type: 'primary',
          label: '取消',
          onClick: val => {
            this.setState({ showAcceptanceDialog: false })
          }
        }
      ],
      groups: [
        {
          items: [
            {
              key: 'remarks',
              label: '备注',
              component: 'Text',
              rule: { required: false }
            }
          ]
        }
      ]
    }
  }

  startPlanTimes(e) {
    if (e.target.checked === true) {
      const startTime = moment()
        .add('month', 0)
        .format('YYYY-MM-' + '01')
      this.eventChangeQuery({ startPlanTime: startTime, startPlanCheck: e.target.checked })
    } else if (e.target.checked === false) {
      this.eventChangeQuery({ startPlanTime: '', startPlanCheck: e.target.checked })
    }
  }
  render() {
    return (
      <div style={{ paddingBottom: '40px' }}>
        {((((this.props.location.query && this.props.location.query.batch) != null ? this.props.location.query.batch : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.batch))) == 0 || (((this.props.location.query && this.props.location.query.status) != null ? this.props.location.query.status : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status))) == 4) && (((this.props.location.query && this.props.location.query.acceptance) != null ? this.props.location.query.acceptance : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.acceptance))) !== 0  && (
          <SearchBar
            placeholder="设备名称/设备编号/安装位置"
            showCancelButton
            cancelText="搜索"
            defaultValue={this.state.queryParam.assetName}
            // onChange={this.searchSubmit.bind(this)}
            onSubmit={this.searchSubmit.bind(this)}
            onClear={this.searchSubmit.bind(this, '')}
            onCancel={this.searchSubmit.bind(this)}
          />
        )}
        {((((this.props.location.query && this.props.location.query.batch) != null ? this.props.location.query.batch : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.batch))) == 0 || (((this.props.location.query && this.props.location.query.status) != null ? this.props.location.query.status : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status))) == 4) && (((this.props.location.query && this.props.location.query.acceptance) != null ? this.props.location.query.acceptance : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.acceptance))) !== 0 && (
          <Form>
            <SelectOrgNew
              label="所属科室"
              style={{ height: '2.8rem', lineHeight: '2.8rem' }}
              defaultValue={this.state.queryParam.clinicalId || this.state.queryParam.siteUID}
              org={this.state.org}
              clearable
              // onChange={e => this.eventChangeQuery({ clinicalId: e.clinicalDeptUID })}
              disableOrg={this.disableOrg}
              onChange={this.onOrgSelect}
            />
            <Select
              defaultValue={this.state.queryParam.ruleLevel}
              item={{ key: 'ruleLevel', label: '保养等级', type: 'ruleLevel' }}
              onChange={e => this.eventChangeQuery({ ruleLevel: e })}
            />
            {this.state.queryParam.startPlanCheck == false && (
              <FormCell>
                <CellHeader>
                  <Label>计划时间</Label>
                </CellHeader>
                <CellBody>
                  <Input
                    type="date"
                    value={this.state.queryParam.planTime}
                    onChange={e => this.eventChangeQuery({ planTime: e.target.value })}
                  />
                </CellBody>
                {!!this.state.queryParam.planTime && (
                  <CellFooter>
                    <Icon value="cancel" onClick={() => this.deletePlanTime()} />
                  </CellFooter>
                )}
              </FormCell>
            )}
            <FormCell>
              <CellHeader>
                <Label>计划月份</Label>
              </CellHeader>
              <CellBody style={{ textAlign: 'right' }}>
                <Switch checked={this.state.queryParam.startPlanCheck} onChange={e => this.startPlanTimes(e)} />
              </CellBody>
            </FormCell>
            <FormCell>
              <CellHeader>
                <Label>急救设备</Label>
              </CellHeader>
              <CellBody style={{ textAlign: 'right' }}>
                <Switch checked={this.state.queryParam.emergencyEquipment} onChange={e => this.eventChangeQuery({ emergencyEquipment: e.target.checked })} />
              </CellBody>
            </FormCell>
          </Form>
        )}
        {(((this.props.location.query && this.props.location.query.acceptance) != null ? this.props.location.query.acceptance : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.acceptance))) == 0 && (
          <Form>
            <SelectOrgNew
              label="所属科室"
              style={{ height: '2.8rem', lineHeight: '2.8rem' }}
              defaultValue={this.state.queryParam.clinicalId || this.state.queryParam.siteUID}
              org={this.state.org}
              clearable
              disableOrg={this.disableOrg}
              onChange={this.onOrgSelect} />
            <Select
              defaultValue={this.state.queryParam.ruleLevel}
              item={{ key: 'ruleLevel', label: '保养等级', type: 'ruleLevel' }}
              onChange={e => this.eventChangeQuery({ ruleLevel: e })}
            />
          </Form>
        )}

        {(((this.props.location.query && this.props.location.query.status) != null ? this.props.location.query.status : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status))) === 6 && this.state.isListVisible && (
          <List
            {...this.state.request}
            selectable={this.state.requestAcceptance.selectable}
            onClick={this.eventClick}
            loading={this.state.loading}
            hasMore={this.state.hasMore}
            onMore={this.eventOnMore}
          />
        )}

        {(((this.props.location.query && this.props.location.query.status) != null ? this.props.location.query.status : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status))) !== 6 && this.state.isListVisible && (
          <List
            {...this.state.request}
            onClick={this.eventClick}
            loading={this.state.loading}
            hasMore={this.state.hasMore}
            onMore={this.eventOnMore}
          />
        )}

        {(((this.props.location.query && this.props.location.query.batch) != null ? this.props.location.query.batch : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.batch))) == 0 && (((this.props.location.query && this.props.location.query.acceptance) != null ? this.props.location.query.acceptance : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.acceptance))) !== 0 && !isAppOffline() && this.renderButtons()}
        {(((this.props.location.query && this.props.location.query.status) != null ? this.props.location.query.status : (this.props.location.state && this.props.location.state.cons && this.props.location.state.cons.status))) == 6 && !isAppOffline() && this.renderAcceptanceButtons()}
        {this.state.showDialog && (
          <Dialog title="全部完成(仅限二级/巡检工单)" show={this.state.showDialog} style={{ width: '98%', maxWidth: 'none' }}>
            <GeneralForm
              value={this.state.form}
              buttons={this.generateForm().buttons}
              groups={this.generateForm().groups}
            />
          </Dialog>
        )}
        {this.state.showAcceptanceDialog && (
          <Dialog title="验收" show={this.state.showAcceptanceDialog} style={{ width: '98%', maxWidth: 'none' }}>
            <GeneralForm
              value={this.state.acceptanceForm}
              buttons={this.generateAcceptanceForm().buttons}
              groups={this.generateAcceptanceForm().groups}
            />
          </Dialog>
        )}
      </div>
    )
  }
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
    workflowConfig: state.workflowConfig.get('workflowConfig')
  }
}

export default connect(mapStateToProps)(ConsolePmOrderList)
