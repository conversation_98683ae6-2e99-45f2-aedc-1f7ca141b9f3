import React, { useState, useEffect } from 'react'
import { Form, FormCell, CellHeader, CellBody, CellFooter, Label, Input, Button } from 'react-weui'
import { Form as APMForm } from '@components'
import { urls, rest } from '@constants'
import { browserHistory as history } from 'react-router'
import { Drawer, message, Modal, Button as AButton, Icon } from 'antd'
import { connect } from 'react-redux'
import { cloneDeep } from 'lodash'
import IbPmReportView from './view/view'
import ePmConfig from './config/ePmConfig'
import FirstCall from './firstcall/firstcallConfig'
import { DRConfig, ICSConfig, MammoConfig, MGConfig, RFConfig, CTKAConfig, MRPMConfig, MINMConfig, MIPETCTConfig, MIQILINConfig, MIPETtraceConfig, SurgeryConfig, BMDConfig, USConfig } from './config/Configs'
import { differentFooter } from './view/PMView'
import './view/view.less'
import moment from 'moment'
import querystring from 'query-string'

// 4，5 这两个是保养和远程保养
const SrNumUrl = `${urls.getSRnum}?page=0&pageSize=100&isFindSr=true&${querystring.stringify({eventTypes: [4,5]})}`

const DRconf = [
  {key: `name`, label: '备件名称'} ,
  {key: `Num`, label: '备件号码'} ,
  {key: `qty`, label: '本次保养用量', component: 'Number'} ,
]

export const needDeviceImagesType = [ 'DR', 'ICS', 'Mammo', 'MG', 'RF', 'US', 'CTKA', 'MRPM', 'MRPETPM', 'MI_NM', 'MI_PETCT', 'MI_QILIN', 'MI_PETtrace', 'Surgery', 'BMD' ]
export const USReportTypes = ['US', 'US-ePM', 'US-Elite', 'US-standard']
export const hasPartsTypes = ['RF', 'US', 'MG', 'DR', 'ICS', 'Mammo']


const getItemsTePm = (type) => {
  let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
  let CONFIG = type === 'parts' ? DRConfig : ePmConfig
  const group = CONFIG.find(item => item.title.indexOf(text) > -1)
  if (type === 'parts' && group.items.length <= 0) {
    group.items = group.items.concat(DRconf)
  }
  // get config as template
  return group.items
}

const USType = [3, 31, 32, 33]
const CTType = [2, 21, 22, 23, 24]
const MRType = [1]
const DXRType = [7] //X-ray
const DSAType = [4] //IGS
const ECTType = [6] //NM
const PETCTType = [5, 51]
const MammoType = [8]
const DRType = [7]
const ICSType = [4]

const partsoftype = {
  'DR': DRConfig,
  'Mammo': MammoConfig,
  'ICS': ICSConfig,
  'CTKA': CTKAConfig,
  'MRPM': MRPMConfig,
  'MRPETPM': MRPMConfig,
  'MI_NM': MINMConfig,
  'MI_PETCT': MIPETCTConfig,
  'MI_QILIN': MIQILINConfig,
  'MI_PETtrace': MIPETtraceConfig,
  'Surgery': SurgeryConfig,
  'BMD': BMDConfig,
  'MG': MGConfig,
  'RF': RFConfig,
  'US': USConfig
}

let interval = null
let intervalOfSr = null
let firstUpdate = true
let autoSaveLoading = false
let manualSaveLoading = false

const IbPmReportEditor = props => {
  const { location: { query, state } } = props
  const [asset, setAsset] = useState({})
  const [templates, setTemplates] = useState([])
  const [value, setValue] = useState({})
  const [tempValue, setTempValue] = useState({})
  const [config, setConfig] = useState(null)
  const [loadConfig, setLoadConfig] = useState(null)
  const [title, setTile] = useState('')
  const [template, setTemplate] = useState('')
  const [defaultV, setDefault] = useState(true)
  const [reRenderTemp, setReRenderTemp] = useState(true)
  const [reRender, setReRender] = useState(true)
  const [showPreview, setShowPreview] = useState(false)
  const [isChange, setIsChange] = useState(false)
  const [Type, setType] = useState('')
  const [previewData, setPreviewData] = useState(null)
  const [SrNumList, setSrNumList] = useState([])
  const [localId, setLocalId] = useState(null)
  const [autoSaveData, setAutoSaveData] = useState(null)
  const [editStatus, setEditStatus] = useState(null)

  const ePmConfigExt = (groupItems, type, templateType) => {
    let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
    let CONFIG = type === 'parts' ? partsoftype[templateType] : ePmConfig
    if (templateType === 'MRPM') {
      CONFIG = CONFIG.filter(v => v.key !== 'PETMaintain')
    }
    const config = cloneDeep(CONFIG) || []
    const group = config.find(item => item.title.indexOf(text) > -1)
    group && (group.items = groupItems.map(item => item.component !== 'Element' ? item : {
      ...item, render: ({ props: { groups }, value }) => <AButton block size="small" type="danger"
        onClick={() => removeItemsById(item.key, groups, value, type)}>{ type === 'parts' ? '删除备件↑' : '删除探头↑' }</AButton>
    }))
    return config
  }

  const clear = (tempValue) => {
    const keys = Object.keys(tempValue)
    keys && keys.map(v => {
      if (v.includes('_')) {
        delete tempValue[v]
      }
    })
    setTempValue({...tempValue})
  }
  const loadReport = async query => {
    const { id, type } = query
    if (templates.length === 0) return
    const url = type ? urls.FirstcallbyID : urls.getReportbyID
    if (type) {
      rest.get(`${url}${id}`, _, true).then(res => {
        const content = JSON.parse(res.content, reviver)
        setValue({ ...value, ...res, content })
        setReRender(false)
      })
    } else {
      rest.get(`${url}${id}`, _, true).then(res => {
        if (res) {
          const owner = res.owner ? res.owner : props.myself.userAccount.name
          const eventDate = res.eventDate ? res.eventDate : moment().format("YYYY-MM-DD")
          const content = JSON.parse(res.content, reviver)
          setValue({ ...value, ...res, owner, eventDate, content })
          //处理tempValue
          if (res.template === 'US-ePM') {
            const value = JSON.parse(res.content, reviver).value
            const config = ePmConfigExt(JSON.parse(res.content, reviver).config)
            handleTempValue(value)
            setConfig(config)
            setLoadConfig(config)
          } else if (hasPartsTypes.includes(res.template)) {
            const value = JSON.parse(res.content, reviver).value
            handleTempValue(value)

            const item = JSON.parse(res.content, reviver).config.find(item => item.title.indexOf('保养备件更换记录') > -1).items
            const config = ePmConfigExt(item, 'parts', res.template)
            setConfig(config)
            setLoadConfig(config)
          } else {
            const value = JSON.parse(res.content, reviver).value
            delete content.value
            handleTempValue(value)
          }

          setDefault(true)
          selectTemp(res.template, { ...value, ...res, owner, eventDate, content })
        } else {
          message.error('加载报告失败')
        }
      }).finally(() => {
        setDefault(true)
      })
    }
  }

  const handleTempValue = (data) => {
    let dataValue = {}
    data.map(v => {
      dataValue = { ...dataValue, ...v }
    })
    setTempValue({ ...tempValue, ...dataValue })
  }

  const initValue = () => {
    const owner = value.owner ? value.owner : props.myself.userAccount.name
    const eventDate = value.eventDate ? value.eventDate : moment().format("YYYY-MM-DD")
    setValue({ ...value, owner, eventDate, srNum: '' })
  }

  const getTemplate = () => {
    const templates = require('./templates').default
    let epmConfig = templates.find(item => item.type === 'US-ePM')
    if (epmConfig) {
      epmConfig.config = ePmConfig
    }
    setTemplates(templates)
    return
    rest.get(`${urls.getTemplate}`, _, true).then(res => {
      if (res && res.value) {
        setTemplates(JSON.parse(res.value))
      } else {
        setTemplates(require('./templates.js'))
      }
    })
  }
  useEffect(() => {
    getTemplate()
    return () => {
      firstUpdate = true
    }
  }, [])

  useEffect(() => {
    setDefault(false)
    if (templates.length === 0) return
    if (query.id) {
      loadReport(query)
    } else if (state && state.asset) {
      setAsset(state.asset)
      const owner = state.asset.owner ? state.asset.owner : props.myself.userAccount.name
      const eventDate = state.asset.eventDate ? state.asset.eventDate : moment().format("YYYY-MM-DD")
      const hospitalName = state.asset.hospitalName || state.asset.hospitalNameV2
      const srNum = state.eventUid ? state.eventUid : ''
      const type = state.asset.geDeviceType
      const templateT = handleType(type)
      getSr(state.asset.systemId)
      setValue({ ...value, srNum, owner, eventDate, systemId: state.asset.systemId, hospitalName, assetName: state.asset.name, template: templateT })
      selectTemp(templateT, { ...value, srNum, owner, eventDate, systemId: state.asset.systemId, hospitalName, assetName: state.asset.name, template: templateT })
    } else if (query.systemId) {
      // 从ole的iframe入口
      const systemId = JSON.parse(query.systemId)
      console.log('ole', systemId);
      getSr(systemId)
      rest.get(`${urls.searchId}/${systemId}`, {}, true).then(res => {
        if (res && res[0]) {
          const asset = res[0]
          setAsset(asset)
          const owner = asset.owner ? asset.owner : props.myself.userAccount.name
          const eventDate = asset.eventDate ? asset.eventDate : moment().format('YYYY-MM-DD')
          const hospitalName = asset.hospitalName || asset.hospitalNameV2
          const type = asset.geDeviceType
          const templateT = handleType(type)
          setValue({
            ...value,
            owner,
            eventDate,
            systemId: asset.systemId,
            hospitalName,
            assetName: asset.name,
            template: templateT
          })
          selectTemp(templateT, {
            ...value,
            owner,
            eventDate,
            systemId: asset.systemId,
            hospitalName,
            assetName: asset.name,
            template: templateT
          })
        }
      })
    } else {
      initValue()
    }
    return () => {
      sessionStorage.clear()
    }
  }, [query, state, templates])

  useEffect(() => {
    setReRenderTemp(true)
  }, [config])

  useEffect(() => {
    clearTimeout(interval)
    if (config && value && value.systemId && value.template && value.template !== 'firstcall' && value.assetName && value.srNum ) {
      if (firstUpdate) {
        firstUpdate = false
      } else {
        setEditStatus('editing')
        if (manualSaveLoading) {
          console.log('正在手动存');
        } else {
          if (intervalOfSr) return
          interval = setTimeout(()=> {
            autoSaveLoading = true
            save(0,'auto')
          }, 5000)
        }
      }
    }
  }, [value, tempValue])

  useEffect(() => {
    clearTimeout(interval)
    clearTimeout(intervalOfSr)
    if (value.srNum) {
      //先输入systemid
      if (!value.systemId) {
        message.warn('请先输入System Id')
        clearSr()
        return
      }
      intervalOfSr = setTimeout(()=> {
        CheckSrNum(value)
      }, 4000)
    }
  }, [value.srNum])

  const clearSr = () => {
    setDefault(false)
    setValue({ ...value, srNum: '' })
  }

  const CheckSrNum = (value) => {
    rest.get(`${SrNumUrl}&eventUid=${value.srNum}&systemId=${value.systemId}`).then(res => {
      clearTimeout(intervalOfSr)
      intervalOfSr = null
      if (res && Array.isArray(res) && res.length > 0) {
        if (res[0].weChatReportId) {
          Modal.confirm({
            title: '存在已发布的中文保养报告，是否重新提交以覆盖？',
            okText: '是',
            cancelText: '否',
            onOk: () => {},
            onCancel: () => {
              clearSr()
            }
          })
        }
      } else {
        Modal.confirm({
          title: '当前设备下该SR#不存在，请重新输入',
          okText: '确认',
          cancelText: '返回',
          className: 'checksrnum-backbtn',
          cancelButtonProps: { ghost: true },
          onOk: () => {
            clearSr()
          }
        })
      }
    })
  }

  const getSr = (systemId) => {
    // 获取SrNum
    //status 1=未关单 2=已关单 不传=全部
    // &eventUid=${'1-8880044131'}
    rest.get(`${SrNumUrl}&systemId=${systemId}`).then(res => {
      // 生成半年内未发布报告的工单号列表
      const list = (res || [])
        .filter(v => !v.weChatReportId && moment(v.createTime).isAfter(moment().subtract(180, 'days')))
        .map(v => v.eventUid)
      
      // 添加默认选项并去重
      setSrNumList([
        { key: '', value: '', title: '请选择' },
        ...Array.from(new Set(list)).map(v => ({ key: v, value: v, title: v }))
      ])
    })
  }

  const headerChange = val => {
    let ischange = false
    if (val.template != template && template != '') {
      setReRenderTemp(false)
      setIsChange(true)
      ischange = true
      // setLoadConfig(ePmConfig)
    }
    setValue({ ...value, ...val, systemId: value.systemId })
    if (val.template != template) {
      // 切换模板清空探头值
      clear(tempValue)
    }
    if (val.template === "firstcall") {
      setReRender(false)
    } else {
      setReRender(true)
      selectTemp(val.template, { ...value, ...val, systemId: value.systemId })
      if (val.template === 'US-ePM') {
        handleConfig(val, 'probe', ischange)
      } else if (hasPartsTypes.includes(val.template)) {
        handleConfig(val, 'parts', ischange)
      }
    }
  }

  const handleConfig = (val, type, ischange) => {
    let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
    let CONFIG = type === 'parts' ? cloneDeep(partsoftype[val.template]) : ePmConfig
      let items
      if (loadConfig) {
        // 已有配置保留配置
        if (!ischange) {
          items = loadConfig.find(item => item.title.indexOf(text) > -1).items
        } else {
          items = CONFIG.find(item => item.title.indexOf(text) > -1).items
        }
      } else {
        // 没有配置设置新配置
        items = CONFIG.find(item => item.title.indexOf(text) > -1).items
      }
      const config = ePmConfigExt(items, type, val.template)
      const group = config.find(item => item.title.indexOf(text) > -1)
      if (val.template != template || ischange) {
      // 切换模板清空探头值和探头配置
        group.items = []
        clear(tempValue)
      }
      Object.assign(group, { extra: <div style={{ color: '#00B9E6' }} onClick={() => addGroupItems(config, type)}>{ type === 'parts' ? '备件＋' : '探头＋'}</div> })
      setConfig(config)
  }
  const removeItemsById = (id, groups, value, type) => {
    const remove = () => {
      let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
      const _new_config = [...groups]
      const group = _new_config.find(item => item.title.indexOf(text) > -1)
      group.items = group.items.filter(item => item.key.indexOf(id) < 0)
      setConfig(_new_config)
      // 删除对应数据
      delete value[`name_${id}`]
      delete value[`image_${id}`]
      delete value[`duration_${id}`]
      delete value[`color_${id}`]
      delete value[`result_${id}`]
      delete value[`serialNum_${id}`]
      delete value[`Num_${id}`]
      delete value[`qty_${id}`]
      setTempValue({...value})
      message.success(`删除成功`)
    }
    Modal.confirm({ title: '确认删除', onOk: remove })
  }
  const templateChange = val => {
    setIsChange(false)
    const selectedTemplate = templates.find(item => item.type === template)
    if (selectedTemplate.type === "US-ePM") {
      val && setTempValue({ ...tempValue, ...val })
    } else if (hasPartsTypes.includes(selectedTemplate.type)) {
      addFrontandBack(selectedTemplate, val, false)
      val && setTempValue({ ...tempValue, ...val })
    } else {
      const imgData = addFrontandBack(selectedTemplate, val, true)
      setTempValue({ ...imgData })
    }
  }

  const addFrontandBack = (selectedTemplate, val, flag) => {
    let imgData = {}
    selectedTemplate && selectedTemplate.config.map(v => {
      v.items.map(item => {
        if (val[item.key]) {
          if (item.key.indexOf('对比') > -1 || item.key.indexOf('Comparison') > -1) {
            if (val[item.key].length % 2 !== 0) {
              document.getElementById(item.label) && (document.getElementById(item.label).innerText = '后')
            } else {
              document.getElementById(item.label) && (document.getElementById(item.label).innerText = '前')
            }
          }
          flag && (imgData[item.key] = val[item.key])
        }
      })
    })
    return imgData
  }
  const addGroupItems = (cfg, type) => {
    let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
    const value = { ...tempValue }
    const group = cfg.find(item => item.title.indexOf(text) > -1)
    const id = (Math.random() + 1).toString(36).substring(7)
    const conf = type === 'parts' ? DRconf : getItemsTePm(type)
    group.items = group.items.concat(conf.map(item => {
          const key = item.key + '_' + id
          value[key] = ''
          return { ...item, key }
    }))
    group.items.push({
      key: id,
      component: 'Element',
      render: ({ props: { groups }, value }) => <AButton block size="small" type="danger"
        onClick={() => removeItemsById(id, groups, value, type)}>{ type === 'parts' ? '删除备件↑' : '删除探头↑' }</AButton>
    })
    setConfig(cfg)
    setLoadConfig(cfg)
    setIsChange(false)
  }
  const setSubAssetGroupItems = (selectedCfg, asset, type) => {
    let text = type === 'parts' ? '保养备件更换记录' : '探头性能远程检测'
    setReRenderTemp(false)
    const group = selectedCfg.find(item => item.title.indexOf(text) > -1)
    // clear
    group && (group.items = [])
    // add button
    group && Object.assign(group, { extra: <div style={{ color: '#00B9E6' }} onClick={() => addGroupItems(selectedCfg, type)}>{ type === 'parts' ? '备件＋' : '探头＋'}</div> })
    // add group items by sub assets
    const values = { ...tempValue }
    if (asset && asset.subAssets && asset.subAssets.length > 0) {
      const conf = type === 'parts' ? DRconf : getItemsTePm(type)
      asset.subAssets.forEach(asset => {
        const id = asset.id.toString()
        group.items = group.items.concat(conf.map(item => {
          const key = item.key + '_' + id
          values[key] = asset[item.key]
          return { ...item, key }
        }))
        group.items.push({
          key: id,
          component: 'Element',
          render: ({ props: { groups }, value }) => <AButton block size="small" type="danger"
            onClick={() => removeItemsById(id, groups, value, type)}>{type === 'parts' ? '删除备件↑' : '删除探头↑'}</AButton>
        })
      })
    }
    values && Object.keys(values).length > 0 && setTempValue(values)
    setConfig(selectedCfg)
    setLoadConfig(selectedCfg)
    // setReRenderTemp(true)
  }
  const selectTemp = (template, value, asset) => {
    const selectedTemplate = templates.find(item => item.type === template) || {}
    const clearitem = selectedTemplate.config ? selectedTemplate.config.find(item => item.key === 'data') : null
    if (clearitem) { clearitem.items = [] }
    if (template === 'MRPM') {
      selectedTemplate.config = selectedTemplate.config.filter(v => v.key !== 'PETMaintain')
    }
    setTile(selectedTemplate.name || '')
    setTemplate(template)
    if (selectedTemplate) {
      setValue({ ...value, content: selectedTemplate })
      if (selectedTemplate.type === 'US-ePM') {
        const hasConfig = value && value.template === 'US-ePM' && value.content && Array.isArray(value.content.config) && value.content.config.length > 0
        if (hasConfig && !asset) {
          const config = ePmConfigExt(value.content.config)
          const group = config.find(item => item.title.indexOf('探头性能远程检测') > -1)
          // add button
          Object.assign(group, { extra: <div style={{ color: '#00B9E6' }} onClick={() => addGroupItems(config)}>探头＋</div> })
          setConfig(config)
        } else {
          setSubAssetGroupItems(cloneDeep(selectedTemplate.config), asset, 'probe')
        }
      } else if (needDeviceImagesType.includes(selectedTemplate.type)) {
        const item = value && value.content && Array.isArray(value.content.config) && value.content.config.find(v => v.title.indexOf('保养备件更换记录') > -1)
        const hasConfig = value && value.template === selectedTemplate.type && value.content && item && Array.isArray(item.items)
        if (hasConfig && !asset) {
          const item1 = value.content.config.find(item => item.title.indexOf('保养备件更换记录') > -1).items
          const config = ePmConfigExt(item1, 'parts', selectedTemplate.type)
          const group = config.find(item => item.title.indexOf('保养备件更换记录') > -1)
          // add button
          group && Object.assign(group, { extra: <div style={{ color: '#00B9E6' }} onClick={() => addGroupItems(config, 'parts')}>备件＋</div> })
          setConfig(config)
        } else {
          setSubAssetGroupItems(cloneDeep(selectedTemplate.config), asset, 'parts')
        }
          // setConfig(selectedTemplate.config)
      } else {
        setConfig(FillImageUploadConfig(selectedTemplate.config))
      }
    } else {
      setConfig(null)
    }
  }
  const handleType = (type) => {
    let templateT = ''
    if (USType.includes(type)) {
      setType('USType')
    } else {
      setType('exceptUSType')
    }
    // USType.includes(type) && (templateT = 'US-ePM');
    CTType.includes(type) && (templateT = 'CT');
    MRType.includes(type) && (templateT = 'MR');
    // DXRType.includes(type) && (templateT = 'DXR')
    DSAType.includes(type) && (templateT = 'DSA');
    ECTType.includes(type) && (templateT = 'ECT');
    PETCTType.includes(type) && (templateT = 'PETCT');
    MammoType.includes(type) && (templateT = 'Mammo');
    DRType.includes(type) && (templateT = 'DR');
    ICSType.includes(type) && (templateT = 'ICS');
    return templateT
  }
  const systemIdChange = systemId => {
    setReRenderTemp(false)
    setIsChange(false)
    getTemplate()
    const keys = Object.keys(tempValue)
      keys && keys.map(v => {
        if (v.includes('_')) {
          delete tempValue[v]
        }
      })
    setDefault(false)
    rest.get(`${urls.searchId}/${systemId}?isapmib=0`, {}, true).then(res => {
      if (!res) {
        message.error('查询失败')
        setTemplate(template)
        setReRenderTemp(true)
        setType('')
        return
      } else if (res.length === 0) {
        message.error('无数据')
        setTemplate(template)
        setReRenderTemp(true)
        setType('')
        return
      }
      setAsset(res[0])
      const owner = res[0].owner ? res[0].owner : props.myself.userAccount.name
      const eventDate = res[0].eventDate ? res[0].eventDate : moment().format("YYYY-MM-DD")
      const hospitalName = res[0].hospitalName || res[0].hospitalNameV2
      const type = res[0].geDeviceType
      const templateT = query.type === 'firstcall'? 'firstcall' : handleType(type)
      setValue({ ...value, owner, eventDate, systemId, hospitalName, assetName: res[0].name, template: templateT })
      setDefault(true)
      getSr(systemId)
      if (query.type === 'firstcall') {
        setReRender(false)
        return
      }
      selectTemp(templateT, { ...value, owner, eventDate, systemId, hospitalName, assetName: res[0].name, template: templateT }, res[0])
      // setReRenderTemp(true)
    }).finally(() => {
      setDefault(true)
    })
  }
  const onClose = () => {
    setShowPreview(false)
    document.body.style.overflow = 'initial'
  }

  const _getTemplate = () => {
    let epmConfigs = templates.find(item => item.type === 'US-ePM')
    if (epmConfigs) {
      epmConfigs.config = [...ePmConfig]
    }
    setTemplates(templates)
  }
  const handleVal = (value, headerValue, type) => {
    const keys = value ? Object.keys(value) : []
    let imgValue = []
    let configArr = []
    config.map(v => {
      v.items.map(item => {
        configArr.push(item.key)
      })
    })
    keys.length >= 1 && keys.map(v => {
      if (value[v] && configArr.includes(v)) {
        imgValue.push({ [v]: value[v] })
      }
    })
    headerValue.content && (headerValue.content.value = imgValue)
    if (hasPartsTypes.includes(type)) {
      const group = headerValue.content.config.find(item => item.title.indexOf('保养备件更换记录') > -1)
      let arr = []
      let id
      const pushData = (id) => {
        arr.push({
          key: `name_${id}`, label: '备件名称' ,
        })
        arr.push({
          key: `Num_${id}`, label: '备件号码' ,
        })
        arr.push({
          key: `qty_${id}`, label: '本次保养用量', component: 'Number' ,
        })
        arr.push({
          key: `${id}`, component: 'Element' ,
          render: ({ props: { groups }, value }) => <AButton block size="small" type="danger"
          onClick={() => removeItemsById(id, groups, value, 'parts')}>删除备件↑</AButton>
        })
      }
      headerValue.content.value.forEach(v => {
        if(Object.keys(v)[0].indexOf('name') > -1 && Object.keys(v)[0].indexOf('_') == -1) {
          id = '0'
          arr = DRConfig.find(item => item.title.indexOf('保养备件更换记录') > -1).items
          arr.push({
            key: `${id}`, component: 'Element' ,
            render: ({ props: { groups }, value }) => <AButton block size="small" type="danger"
            onClick={() => removeItemsById(id, groups, value, 'parts')}>删除备件↑</AButton>
          })
        } else if (Object.keys(v)[0].indexOf('name_') > -1) {
          id = Object.keys(v)[0].split('_')[1]
          pushData(id)
        }

      })
      group.items = arr
    }
    const formData = { ...headerValue, srNum: headerValue.srNum ? headerValue.srNum.trim() : headerValue.srNum }
    return formData
  }
  const handleUSVal = (value, headerValue) => {
    let arr = []
    if(!Array.isArray(value)) {
      arr = Object.keys(value).map(v => {
        return {[v]: value[v]}
      })
    } else {
      arr = value
    }
    headerValue.content && (headerValue.content.value = arr);
    headerValue.content && (headerValue.content.config = getSubAssetConfigItems(config));
    const formData = { ...headerValue, srNum: headerValue.srNum ? headerValue.srNum.trim() : headerValue.srNum};
    return formData
  }
  const getSubAssetConfigItems = config => {
    const group = config.find(item => item.title.indexOf('探头性能远程检测') > -1)
    return group.items
  }

  const getPartsValue = (data) => {
    let valueObj = {}
    let ids = []
    let arr = []
    data.forEach(v => {
      valueObj = Object.assign(valueObj, v)
    })
    Object.keys(valueObj).forEach(v => {
      if (v.includes('qty') || v.includes('name') || v.includes('Num')) {
        ids.push(v.split('_')[1])
      }
    })
    Array.from(new Set(ids)).forEach(id => {
      id && arr.push({ name: valueObj[`name_${id}`], Num: valueObj[`Num_${id}`], qty: valueObj[`qty_${id}`] })
    })
    return arr
  }
  const save = (status, autoSave) => {
    _getTemplate()
    let formData = {}
    if (value.template === "US-ePM") {
      formData = handleUSVal(tempValue, value)
    } else {
      formData = handleVal(tempValue, value, value.template)
    }
    const id = formData.id ? formData.id : (localId ? localId: null)
    if (hasPartsTypes.includes(formData.template)) {
      const partsValue = getPartsValue(formData.content.value)
      formData.content.value.push({ 'ReplacedParts': partsValue })
    }
    let newarr = [...formData.content.value]
    formData.content.value.map(v => {
      let k = Object.keys(v)[0]
      if (v[k] === 'nouse') {
        newarr.map(item => {
          let newk = Object.keys(item)[0]
          const resName = k.split('Res')[0] || k.split('Res')[1]
          if(newk.includes(resName) && newk !== k) {
            Array.isArray(v[k]) && (item[newk] = []);
            !Array.isArray(v[k]) && (item[newk] = '');
          }
        })
      }
    })
    formData.content.value = newarr
    if (value.template === 'Mammo') {
      let valueObj = {}
      formData.content.value.forEach(v => {
        valueObj = Object.assign(valueObj, v)
      })
      formData.content.value.push({ deviceImages: valueObj.deviceName })
    }
    // console.log('formData', formData.content.value);
    if (status === '预览') {
      setShowPreview(true)
      setPreviewData(formData)
      document.body.style.overflow = 'hidden'
      return
    }
    if (!formData.systemId) {
      message.error('设备编号不能为空')
      return
    }
    if (!formData.assetName) {
      message.error('机器型号不能为空')
      return
    }
    if (!formData.srNum) {
      message.error('服务单号不能为空')
      return
    }
    if (status == 1) {
      if (!formData.hospitalName) {
        message.error('医院名称不能为空')
        return
      }
      if (!formData.owner) {
        message.error('保养工程师不能为空')
        return
      }
      if (value.template.indexOf('US') <= -1 && !differentFooter.includes(value.template) && !formData.nextEventDate || !formData.eventDate) {
        message.error('保养时间不能为空')
        return
      }
      if ((value.template.indexOf('US') > -1 || differentFooter.includes(value.template)) && !formData.eventDate) {
        message.error('保养时间不能为空')
        return
      }
      if ((value.template.indexOf('US') <= -1 && !needDeviceImagesType.includes(value.template)) && !formData.summary) {
        message.error('保养简述不能为空')
        return
      }
      if (value.template === 'Mammo' || value.template === 'MG') {
        let valueObj = {}
        formData.content.value.forEach(v => {
          valueObj = Object.assign(valueObj, v)
        })
        if ((valueObj.deviceName === 'Seno Crystal' || valueObj.deviceName === 'Crystal NOVA') && (!valueObj['JCPM013ATest'] || (valueObj['JCPM013ATest'] && valueObj['JCPM013ATest'].length <= 0))) {
          message.error('JC PM 013A的测试结果不能为空')
          return
        }
      }
    }
    const data = { ...autoSaveData, ...formData, status, id, content: JSON.stringify(formData.content, replacer, 2) }
    setEditStatus('saving')
    rest.post(urls.saveReport, data).then(res => {
      if (res) {
        if (autoSave) {
          if (!id) {
            window.history.pushState({},'', `/wx/report/pm/edit?id=${res.id}`)
            setLocalId(res.id)
            setAutoSaveData(res)
          }
        } else {
          if (status === 0) {
            if (formData.id) {
              message.success('修改成功');
              history.goBack();
            } else {
              message.success('保存成功')
              window.history.pushState({},'', `/wx/report/pm/edit?id=${res.id}`)
              setLocalId(res.id)
              setAutoSaveData(res)
            }
          } else if (status === 1) {
            message.success('发布成功')
            rest.post(urls.pushMessage, { ...data, id: res.id })
            history.replace({ pathname: '/wx/report/pm/view', query: { id: res.id } })
          }
        }
        setEditStatus('saved')
        let editStatusId = setTimeout(() => {
          setEditStatus(null)
          clearTimeout(editStatusId)
        }, 2000)
      } else {
        message.error('操作失败')
      }
      manualSaveLoading = false;
      autoSaveLoading = false
    })

  }

  return (
    <div>
      <h2 className='title-text'>{query.type === 'firstcall' ? '探头检测报告编辑': '保养报告编辑'}</h2>
      {editStatus && editStatus === 'editing' && <div className='title-icon'><Icon type="edit" /><span className='blank'></span><span className='dot'></span></div>}
      {editStatus && editStatus === 'saving' && <div className='title-icon'><Icon type="cloud-sync" /><Icon type="sync" spin style={{ position: 'absolute', fontSize: 7, bottom: 6, left: 14, zIndex: 101 }} /><span className='blank2'></span></div>}
      {editStatus && editStatus === 'saved' &&  <div className='title-icon'><Icon type="cloud-upload" /></div>}
      <SysteIdEditor onChange={systemIdChange} value={value} setValue={setValue} />
      <HeaderEditor query={query} SrNumList={SrNumList} value={value} onChange={headerChange} Type={Type} templates={templates} defaultV={defaultV} setDefault={setDefault} template={template} />
      {reRender && template && config && <TemplateEditor setEditStatus={setEditStatus} reRenderTemp={reRenderTemp} title={title} value={tempValue} config={config} save={save} onChange={templateChange} setReRenderTemp={setReRenderTemp} />}
      {!reRender && <FirstCall setEditStatus={setEditStatus} headerValue={value} setShowPreview={setShowPreview} setPreviewData={setPreviewData} query={query} />}
      <Drawer
        className="pmedit"
        title="预览"
        placement="bottom"
        closable={true}
        onClose={onClose}
        visible={showPreview}
        height={'90%'}
      >
        {showPreview && <IbPmReportView previewData={previewData} value={value} isPreview={showPreview} />}
      </Drawer>
    </div>
  )
}

const FillImageUploadConfig = config => {
  if (!config) return
  return config.map(group => ({
    title: group.title,
    items: group.items.map(item => ({
      key: item.key || item,
      label: item.label || item,
      component: item.component || "ImageUploader",
      rule: { min: item.min || group.min || null },
      props: item.component ? {
        title: item.label || item,
      } : {
        title: item.label || item,
        autoSave: true,
        maxCount: item.limit || group.limit || 1,
      }
    })),
  }))
}

const SysteIdEditor = props => {
  const [value, setValue] = useState('')
  useEffect(() => {
    setValue(props.value.systemId)
  }, [props.value.systemId])
  const onClick = () => {
    if (value && props.onChange) {
      props.onChange(value)
    }
  }
  const IdChange = (e) => {
    if (props.setValue) {
      props.setValue({ ...props.value, systemId: e.target.value.trim() })
    }
    setValue(e.target.value.trim())
  }

  return (
    <div className="edit-form">
      <Form>
        <FormCell vcode>
          <CellHeader>
            <Label>设备编号</Label>
          </CellHeader>
          <CellBody>
            <Input type="text" placeholder="请输入System Id" value={value} onChange={IdChange} />
          </CellBody>
          <CellFooter>
            <Button type="vcode" style={{ color: '#00B9E6', fontSize: '15px' }} onClick={onClick}>查询</Button>
          </CellFooter>
        </FormCell>
      </Form >
    </div>
  )
}

const HeaderEditor = props => {
  const { query } = props
  const [value, setValue] = useState(null)

  const sortFun = (a, b) => {
    if(/^[a-z]/i.test(a.value) || /^[a-z]/i.test(b.value)){
      return a.value.localeCompare(b.value, 'en')
    }else {
      return a.value.localeCompare(b.value, 'zh-cn')
    }
   }
  let templateItem
  if (query.type === 'firstcall') {
      templateItem = props.templates.filter(v => v.type === 'firstcall')
  } else {
    if (props.Type === 'USType') {
      templateItem = props.templates.filter(v => v.type.indexOf('US') > -1 && v.type !== 'firstcall')
    } else if (props.Type === 'exceptUSType') {
      templateItem = props.templates.filter(v => v.type.indexOf('US') <= -1 && v.type !== 'firstcall' )
    } else {
      templateItem = props.templates.filter(v => v.type !== 'firstcall' )
    }
  }
  const templateItems = templateItem.map(temp => ({ key: temp.type, value: temp.name })).sort(sortFun)
  useEffect(() => {
    setValue({ ...value, ...props.value })
    props.setDefault(true)
  }, [props.value])
  const groups = [
    {
      title: '设备情况',
      items: [
        { key: 'assetName', label: '机器型号', rule: { required: true } },
        { key: 'hospitalName', label: '医院名称', rule: { required: true } },
        { key: 'srNum', label: '服务单号', ref:'请先查询设备编号', rule: { required: true }, tree: true, component: 'Select', options: props.SrNumList || []},
      ]
    },
    {
      title: '保养情况',
      items: [
        { key: 'owner', label: '保养工程师', rule: { required: true }, hide: val => query.type === 'firstcall' },
        { key: 'eventDate', label: '本次保养日期', component: 'Date', rule: { required: true }, hide: val => query.type === 'firstcall' },
        { key: 'nextEventDate', label: '预计下次保养日期', component: 'Date', rule: differentFooter.includes(props.template) || !props.template? null : { required: true, gt: 'eventDate' }, hide: val => USReportTypes.includes(val.template) || query.type === 'firstcall' },
        {
          key: 'nextEventDateTip',
          component: 'Label',
          style: { color: '#ccc', fontSize: 14 },
          content: '实际保养日期依相互预约为准',
          hide: val => USReportTypes.includes(val.template) || query.type === 'firstcall'
        },
        { key: 'summary', label: '本次保养简述', component: 'TextArea', props: { placeholder: '本次保养简述', rows: 4 }, rule: { required: true }, hide: val => USReportTypes.includes(val.template) || needDeviceImagesType.includes(val.template) || query.type === 'firstcall'},
        { key: 'template', label: '报告模板', component: 'Select', options: templateItems },
      ]
    }
  ]

  if (value) {
    return (props.defaultV && <APMForm value={value} groups={groups} onChange={props.onChange} />)
  } else {
    return null
  }
}

const TemplateEditor = props => {
  const { save, config, title, value, onChange, reRenderTemp, setReRenderTemp } = props
  useEffect(() => {
    setReRenderTemp(true)
  }, [])

  const _save = (status) => {
    clearTimeout(interval)
    if(autoSaveLoading) {
      message.info('正在为您自动暂存, 请稍后点击...');
    } else {
      manualSaveLoading = true;
      save(status)
    }
  }

  const buttons = [
    { label: '📩 暂存', props: { size: 'small' }, type: 'default', onClick: val => _save(0)},
    { label: '🔍 预览', props: { size: 'small' }, type: 'default', onClick: val => save('预览') },
    { label: '📤 发布', props: { size: 'small' }, type: 'primary', validation: true, confirm: '确认发布报告?', onClick: val => _save(1)},
  ]

  return (
    <div>
      <h3 style={{ textAlign: 'center', marginTop: '10px' }}>{title}</h3>
      {reRenderTemp && <APMForm value={value} groups={config} buttons={buttons} onChange={onChange} />}
      {/* <div style={{ paddingBottom: 'env(safe-area-inset-bottom)' }} /> */}
    </div>
  )
}

function mapStateToProps(state) {
  return {
    myself: state.preload.myself,
  }
}

function replacer(key, value) {
  // if we get a function, give us the code for that function
  if (typeof value === 'function') {
    return value.toString();
  }
  return value;
}

function reviver(key, value) {
  if (typeof key === 'string' && key.indexOf('function ') === 0) {
    let functionTemplate = `(${value})`;
    return eval(functionTemplate);
  }
  return value;
};


export default connect(mapStateToProps)(IbPmReportEditor);
