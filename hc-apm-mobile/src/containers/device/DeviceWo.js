import React, { useState, useEffect } from "react";
import { CellBody, Cells, Cell, CellFooter } from 'react-weui'
import { rest, urls } from '@/constants'
import { APMList } from '../../components'
import moment from 'moment'
import { Drawer } from "antd"

export const DeviceWo = props => {
  const { assetId } = props
  const [count, setCount] = useState(0)
  const [config, setConfig] = useState({
    key: 'wo-history',
    title: '历史维修记录',
    url: urls.workOrderList,
    query: { assetId, pageSize: 5 },
    itemContent: [
      { key: 'repairType', label: '工单类型' },
      { key: 'currentStepName', label: '工单状态' },
      { key: 'srNum', label: '报修编号' },
      { key: 'createdTime', label: '报修时间' },
      { key: 'comment', label: '故障现象' },
      { key: 'ownerName', label: '维修人' },
      { key: 'hasParts', label: '是否更换配件' }
    ],
    getState: res => setCount(res.count),
    transform: data => ({ ...data, createdTime: moment(data.createdTime).format('YYYY-MM-DD HH:mm:ss')})
  })
  const [show, setShow] = useState(false)

  return (
    <React.Fragment>
      <Cells>
        <Cell access onClick={() => setShow(true)}>
          <CellBody>历史维修记录</CellBody>
          <CellFooter>{count}</CellFooter>
        </Cell>
      </Cells>
      <Drawer 
        visible={show} 
        placement="bottom" 
        closable={false} 
        onClose={() => setShow(false)} 
        height="60vh" 
        bodyStyle={{ padding: 0 }} 
        getContainer={false}
      >
        <APMList {...config} />
      </Drawer>
    </React.Fragment>
  )
}

export default DeviceWo;

