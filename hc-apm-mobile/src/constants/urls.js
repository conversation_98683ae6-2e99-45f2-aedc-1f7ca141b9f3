const userAccounts = '/api/apm/security/userAccounts'
const users = '/api/apm/security/users'
const usersFilter = users + '/filter'
const userRoles = '/api/apm/security/userroles/:id/userroles'
const objects = '/zuul/hcapmobjecthubservice/api/apm/objectHub/objects'
const masterData = '/hcapmmasterdataservice/api/apm/masterData'
const wechat = '/api/apm/wechat'
const org = masterData + '/orginfos'
const workflow = '/hcapmworkflowservice/api/apm'
const workAsset = '/hcapmassetservice/api/apm'
const report = `/hcapmreportservice/api/apm/report`
const training = `/hcapmtrainingservice/api/apm/trainingservice`

const asset = '/hcapmassetservice/api/apm/asset'
const alarm_ee = asset + '/ee_alert'
const hasHistory = asset + '/assethistory/isHas'
const showHistory = asset + '/assethistory'
const qrCodes = masterData + '/qrcodes'
const assets = asset + '/assetinfos'
const inspection = asset + '/inspectionorders'
const pmOrder = asset + '/pmorders'
const request = asset + '/serviceRequests'
const workorder = asset + '/workOrders'
const workorderSteps = asset + '/workorderSteps'
const workorderDetails = asset + '/workOrderDetails'
const requestMsgs = asset + '/serviceRequestMsgs'
const inventoryOrder = asset + '/inventoryorders'
const inventoryOrderDetail = asset + '/inventoryorderdetails'
const mvsWorkOrders = asset + '/mvsWorkOrders'
const fsoAssets = asset + '/infosharing'
const zkb = '/hcapmassetservice/api/apm/zkbuserknowledge'
const DIreport = '/hcapmreportservice/api/apm/wechatreport/'
const quora = `/hcapmcms/api/apm/cms/qa`
const QWeChatStatistics = '/hcapmassetservice/api/apm/zkb/partOrder/queryWeChatStatistics'
const queryZKbStatisticsList = '/hcapmassetservice/api/apm/zkb/partOrder/queryZKbStatisticsList'
const queryZkbIntehralOrderList = '/hcapmassetservice/api/apm/zkb/integralOrder/queryZkbIntehralOrderList'
const zkbFePartPage = '/hcapmassetservice/api/apm/zkb/zkbfepartorder/zkbFePartPage'

// 备件 start
const sparepartstotal = '/hcapmassetservice/api/apm/parts/inventory'
const sparepartsrequest = '/hcapmassetservice/api/apm/parts/request'
const sparepartswarehouse = '/hcapmassetservice/api/apm/parts/warehouse'
const sparepartsrequestEdit = '/hcapmassetservice/api/apm/parts/requestEdit'
const sparepartstock = '/hcapmassetservice/api/apm/parts/stock'
const sparepartsconfig = '/hcapmassetservice/api/apm/parts/config'
// 备件 end

// 审核 start
const approvals = '/hcapmassetservice/api/apm/approvals'
// 审核 end

const gateway = {
  /** gateway */
  basicAuth: userAccounts + '/authenticateBasic',
  weChatBinding: userAccounts + '/weChatBindings',
  changingPassword: userAccounts + '/weChatBindings/changes',
  weChatAuth: userAccounts + '/authenticateWeChat',
  weChatUnbind: wechat + '/unbind',
  weChatMessageAck: wechat + '/message/ack',
  uploadMateria: wechat + '/materia',
  wxQrCodeImage: '/api/apm/weChat/qrCodeImage',
  wxAccountLink: masterData + '/qrcodes/mplink',
  myself: userAccounts + '/myself',
  weChatInfo: userAccounts + '/myselfwx',
  unauthorizedWeChateInfo: userAccounts + '/unauthorizedwx',
  smsCode: userAccounts + '/smsCode',
  flutterBind: userAccounts + '/app/bind',
  updateUserAccount: userAccounts + '/updateUserAccount',
  createConnUserInfo: userAccounts + '/createConnUserInfo',
  updateDefaultEnv: userAccounts + '/updateDefaultEnv',
  userConnInfo: userAccounts + '/userConnInfo',
  assetConnInfo: userAccounts + '/assetConnInfo',
  optionalDept: userAccounts + '/optional/dept',
  changeDept: userAccounts + '/change',
  userSiteList: userAccounts + '/optional/site',
  userAccounts,
  userInfo: '/hcapmmasterdataservice/api/apm/masterdata/useraccount',
  users,
  userRoles,
  usersFilter,
  hasHistory,
  showHistory,
  QWeChatStatistics,
  queryZKbStatisticsList,
  queryZkbIntehralOrderList,
  zkbFePartPage,
  socketLogin: '/api/apm/qrLogin/noticeQrCodeLogin',
  noticeAddDevice: '/api/apm/qrLogin/noticeAddDevice',
  socketVendorLogin: '/api/apm/qrLogin/noticeScanByVender',

  /** FeiShu apps (far east project) */
  farEastGuest: userAccounts + '/authenticateGuest',
  farEastAuth: userAccounts + '/authenticateFarEast',
  fsApiTicket: userAccounts + '/feishu/jsApiTicket',

  /** DD apis */
  ddUserInfo: userAccounts + '/dingTalk/userInfo',
  authenticateDD: userAccounts + '/dingTalk/authenticate',
  ddBinding: userAccounts + '/dingTalk/dingTalkBindings',
  ddInfo: userAccounts + '/dingTalk/myselfdd',
  ddApiTicket: userAccounts + '/dingTalk/jsApiTicket',

  /** user-activity */
  userActivity: report + '/user-activity',
  periodical: report + `/periodical_reports`,

  /** maintain status */
  sysMaintain: report + '/apm_config_info/system_maintain',

  apmConfigInfo: report + '/apm_config_info',

  /** usicons */
  deviceUsicons: `${report}/apm_config_info/`,

  reportDownload: `${report}/download`,

  /** asset */
  asset,
  assets,
  assetsInfo: assets + '/info',
  assetClassificationTree: asset + '/sysAuthGroup/classificationTree',
  assetTypeGroup: asset + '/assetTypeGroup/classificationTreeByUser',
  assetTypeGroupCount: asset + '/assetTypeGroup/classificationTreeCount',
  assetOleLog: asset + '/ole-operate-log',
  assetCfda: asset + '/cfdainfo',
  assetQrCode: asset + '/qrcodes',
  assetSearch: asset + '/fuzzymatchinfo/match',
  assetTag: asset + '/tags/assets',
  assetFaulty: asset + '/assetfaulttype',
  assetSummary: assets + '/summary',
  assetSummaryIB: assets + '/ib_status_summary',
  assetScanQrCode: assets + '/manualScanQrCode',
  assetAlertConfig: asset + '/fe-alert-config',
  assetOptRecord: asset + '/assetOptRecord/record',
  assetOptRecordNew: asset + '/assetOptRecord/record/getOperationRecords',
  assetOperationRecord: asset + '/assetOptRecord/operation/record',
  coldHead: asset + '/coldheadwarn',
  alertAcknowledged: asset + '/alert_acknowledged',
  zkbFileAttachment: assets + '/zkbFileAttachment',
  specialAssetInfos: asset + '/specialAssetInfos/byAssetUid',
  maintenance: report + '/maintenance',
  eemonitor: report + '/eemonitor',
  mrlog: report + '/mr-syslog',
  coldheader: report + '/coldheader/v2',
  ct: report + '/capture_data/ct',
  ctLogsum: report + '/capture_data/ct/logsum',
  petct: report + '/capture_data/petct',
  nm: report + '/capture_data/nm',
  us: report + '/capture_data/ultra-sound',
  icenterUs: report + '/icenter/data-manager',
  igs: report + '/capture_data/igs',
  extSrSteps: report + '/sr-service/combine',
  ehrReportApply: report + '/ehr/report',
  tube: asset + '/ct/tube',
  tubeConfig: asset + '/ct/tube/config',
  ee_asset: alarm_ee,
  ee_number: alarm_ee + '/asset_number',
  ee_category: alarm_ee + '/category',
  inspection,
  pmOrder,
  inventoryOrder,
  inventoryClinicalDepts: inventoryOrder + '/:orderId/clinicalDepts',
  inventoryOrderDetail: inventoryOrder + '/:orderId/:clinicalDeptUID/details',
  undocumentedDetail: inventoryOrderDetail,
  reportUndocumented: inventoryOrderDetail,
  bathInventory: inventoryOrderDetail + '/bathInventory',
  inventoryOrderIssues: inventoryOrder + '/:orderId/:clinicalDeptUID/issues',
  resolveIssue: inventoryOrder + '/issues',
  reportInventoryIssues: inventoryOrder + '/issues',
  request,
  requestSummaryMine: asset + '/serviceRequests/summary/mySr',
  requestSummaryTeam: asset + '/serviceRequests/summary/teamSr',
  requestMsgs,
  telReport: request + '/report/choose',
  workorder,
  queryMaintenanceContract: workorder + '/queryMaintenanceContract',
  dispatcher: asset + '/workOrdersForDispatcher',
  dispatcherCount: asset + '/workOrdersForDispatcherCount',
  workorderSteps,
  workorderStep: asset + '/workOrder',
  workorderDetails,
  woPickedup: asset + '/workOrdersPickedup',
  woApproveScheduleList: workAsset + '/approveSchedule/getApproveScheduleList',
  workOrderSummary: asset + '/workOrderSummary',
  woToPickup: asset + '/workOrdersToPickup',
  workflowsConfig: asset + '/workflowConfig',
  mvsTenants: mvsWorkOrders + '/tenantList',
  mvsWoCounts: mvsWorkOrders + '/workOrderListCount',
  mvsPoCounts: mvsWorkOrders + '/pmOrderListCount',
  mvsAssetCounts: mvsWorkOrders + '/assetListCount',
  mvsAssetList: mvsWorkOrders + '/assetList',
  mvsAccessAvailable: mvsWorkOrders + '/isAvailableAsset',
  mvsSubUserIds: mvsWorkOrders + '/subUserIds',
  mvsAssetInfo: asset + '/mvsassetinfos',
  updatePrice: workorderDetails,
  nurse: asset + '/nursesite/maintain',
  nurseSummary: asset + '/nursesite/summary',
  nurseMaintainById: asset + '/nursesite/maintainById',
  nurseShowAllPasses: asset + '/nursesite/showAllPasses',
  deptAudit: asset,
  pmOrderAllDone: '/hcapmworkflowservice/api/apm/pmOrders/allDone',
  getPmAcceptor: '/hcapmworkflowservice/api/apm/pmOrders/acceptorForPlaned',
  pmAckNeedSign: '/hcapmworkflowservice/api/apm/pmOrders/pmAckNeedSign',
  getEhrview: report + '/ehr/report/approve',
  serviceRequestsNoAsset: asset + '/serviceRequestsNoAsset',

  /** 计量 */
  measuring: '/hcapmworkflowservice/api/apm/asset/measuring',

  /** deviceRecords */
  workOrderList: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getWorkOrderListByAssetId',    // type：1 进行中的，2:已关单，默认不传查询全部工单
  repairingRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getOpenedWorkOderListByAssetId',
  repairedRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getClosedWorkOderListByAssetId',
  pmingRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getOpenedPmoderListByAssetId',
  pmedRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getClosedPmoderListByAssetId',
  meteringRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getMeasureByAssetId',
  meteredRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getMeasureHistoryByAssetUid',
  checkingRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getNoCheckedNurseSiteMaintain',
  checkedRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getCheckedNurseSiteMaintain',
  switchRecordingRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getOpenAssetOptRecord',
  switchRecordedRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getClosedAssetOptRecord',
  getAssetOperationRecord: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getAssetOperationRecord',
  scrapIdentification: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getTerminationByAssetId',
  adverseEventsReporting: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getOpenAderseEventsByAssetUid',
  adverseEventsReported: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getClosedAderseEventsByAssetUid',
  getMeasureDetailById: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getMeasureDetailById',

  //new add fso
  fsoAssetCounts: fsoAssets + '/assetListCount',
  fsoAssetList: fsoAssets + '/assetList',
  fsoTenants: fsoAssets + '/tenantList',

  /** master data */
  surveyFeReminder: masterData + '/survey/feReminder',
  surveryActivity: masterData + '/survey/userActivity',
  sites: masterData + '/tenantInfos',
  roles: masterData + '/sysroles',
  i18n: masterData + '/i8nMessages',
  schema: masterData + '/schemas',
  qrCodeRule: masterData + '/qrCodeRuleConfig/qrCode',
  org,
  orgList: `${org}/page/institution`,
  orgVisible: `${org}/visible`,
  virtualOrgVisible: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/visible',
  offices: org + '/userOfficeList',
  qrCodes,
  qrCodeLibs: qrCodes + '/qrCode',
  qrCodeImg: qrCodes + '/qrCode/image',
  qrCodeScan: qrCodes + '/qrCodeScan',
  qrCodeBinding: qrCodes + '/binding',
  workflowConfig: masterData + '/workflows/isDispatcher',
  workflowConfigPm: masterData + '/workflows/isPmDispatcher',
  unstructuredData: masterData + '/unstructuredData',
  nameplate: masterData + '/nameplate/fileUrlRC',
  customConfig: masterData + '/CustomConfig',
  entity: masterData + '/entity',
  redis: masterData + '/redis',
  userSignature: masterData + '/userSignature',
  saveSupplier: qrCodes + '/saveSupplier',
  getSurveryStatus: masterData + '/survey/isDone',
  getSurveryValid: masterData + '/survey/isInvalid',
  submitSurvery: masterData + '/survey/submit',
  getSurveryInfo: masterData + '/survey/getInfo',

  /** object */
  obj: objects + '/',
  objUrl: objects + '/urlFile',
  objSingle: objects + '/single',
  objMutiple: objects + '/multiple',
  objDownload: objects + '/download',

  /** download **/
  getAuthCode: '/hcapmobjecthubservice/api/apm/objectHub/objects/getAuthCode',
  downloadByAuthCode: '/hcapmobjecthubservice/api/apm/objectHub/objects/downloadByAuthCode',

  /** workflow */
  pmOrderPickUpCount: workflow + '/pmOrders/pickUpCount',
  pmOrderClinicCount: workflow + '/pmOrders/forClinicalCount',
  pmOrderHeadCount: workflow + '/pmOrders/forHeadCount',
  pmOrderForClinic: workflow + '/pmOrders/forClinical',
  pmOrderForHead: workflow + '/pmOrders/forHead',
  pmOrderPickedUp: workflow + '/pmOrders/pickedUp',
  pmOrderToPickUp: workflow + '/pmOrders/toPickup',
  pmOrderDetail: workflow + '/pmOrders/:id/pmOrder',
  pmOrderSteps: workflow + '/pmOrders/:id/pmOrderSteps',
  pmOrderAction: workflow + '/pmOrders/:id/action',
  pmOrderCancel: workflow + '/pmOrders/:id/cancel',
  pmOrderApprover: workflow + '/pmOrders/:assetUid/approver',
  pmRule: workflow + '/pm/rule',
  pmOrderCreate: workflow + '/pmOrders',
  assetEvents: workflow + '/assetLifecycleEvents',
  pmOrderBatch: workflow + '/pmOrders/batch',
  pmOrderBatchAccept: workflow + '/pmOrders/acceptAll',
  pmOrderCancelBatch: workflow + '/pmOrders/cancel',

  /** 科室间借调 */
  secondment: workflow + '/secondment',

  /** rental */
  allocationSummary: workflow + '/allocation/summary',

  /** 智康保 */
  zkbFiles: zkb + '/zkbUserFileAttachments',
  zkbModels: zkb + '/zkbFileSecondDirectory',
  zkbSp: workAsset + '/zkb/partOrder',
  zkbFe: workAsset + '/zkb/partOrder/queryUnrealCheckBoxList',
  zkbFeModel: workAsset + '/zkb/partOrder/saveUnreal',

  /** kpi */
  kpiList: asset + '/ib-kpi/list',
  kpiFetch: asset + '/ib-kpi/fetch',
  hospitals: report + '/ib-asset-activity-statistics/list',
  activity: report + '/ib-asset-activity-statistics/mine',

  /** remote training */
  getCourse: training + `/session`,
  getFormConfig: training + '/enrollment/field',
  enrollment: training + '/enrollment',
  enrollmentByBrowser: training + '/enrollment/ops/add',
  getUser: training + '/enrollment/new',
  verifyDeadline: training + '/enrollment/verifyDeadline',
  getRegistrationList: training + '/enrollment/getRegistrationList',
  getRegistrationDetailed: training + '/enrollment/getRegistrationDetailed',
  showMyTraining: training + '/enrollment/showMyTraining',
  getHospitalByUserId: training + '/enrollment/getHospitalByUserId',
  verifyRepeatEnrollment: training + '/enrollment/verifyRepeatEnrollment',
  cancelReserve: training + '/enrollment/cancel',

  /** DI保养微报 */
  searchId: assets + '/systemId',
  saveReport: DIreport,
  getReport: DIreport + 'get/',
  delReport: DIreport,
  getReportbyID: DIreport,
  pushMessage: DIreport + 'publish',
  getTemplate: `/hcapmreportservice/api/apm/report/apm_config_info/ib_maintain_templates`,
  saveTemplate: `/hcapmreportservice/api/apm/report/apm_config_info`,
  getSRnum: '/hcapmworkflowservice/api/apm/assetLifecycleEvents/getAssetEventByFuzzyQuery',
  //保养规则
  getMaintenance: '/hcapmworkflowservice/api/apm/pm/rule/asset/',
  /**待归还的拒接 */
  postRefuse: '/hcapmworkflowservice/api/apm/allocation/rent/rejectReturn',
  /** 在借设备编辑*/
  postAnnex: '/hcapmworkflowservice/api/apm/allocation/rent/updateConfirm',

  saveFirstcallreport: '/hcapmreportservice/api/apm/firstCallReport/save',
  FirstcallList: '/hcapmreportservice/api/apm/firstCallReport/query/',
  FirstcallbyID: '/hcapmreportservice/api/apm/firstCallReport/',
  publishFirstcall: '/hcapmreportservice/api/apm/firstCallReport/publish',
  delSingleReport: '/hcapmreportservice/api/apm/firstCallReport/deleteReport',
  delPublishReport: '/hcapmreportservice/api/apm/firstCallReport/deletePublish',

  register: '/hcapmselfservice/api/apm/selfservice/register/registerInfo',
  simpleReg: '/hcapmselfservice/api/apm/selfservice/userprofile/checkPhoneNumberVerificationCode',
  getCodeForReg: '/hcapmselfservice/api/apm/selfservice/userprofile/sendmobileverificationcode',
  getHospitals: '/hcapmselfservice/api/apm/org/service/operation/searchhospital',
  getCount: '/api/apm/web/counter',

  // 一机一码
  searchSID: '/hcapmassetservice/api/apm/asset/assetinfos/systemId',
  searchLCS: '/hcapmassetservice/api/apm/asset/assetinfos/lcs',
  validateCode: '/hcapmassetservice/api/apm/asset/qrcode/verification',
  saveCode: '/hcapmassetservice/api/apm/asset/qrcode/save',
  getAddress: '/hcapmassetservice/api/apm/asset/ge_dictionary',
  getAddressByHopsital: '/hcapmassetservice/api/apm/asset/ge_dictionary/address',

  // 入保通知函
  warrantyInfo: '/hcapmreportservice/api/apm/report/asset_warranty_email/',
  warrantyInfoByAsset: '/hcapmreportservice/api/apm/report/asset_warranty_email/asset/',
  // 出保函
  outofwarrantyInfo: '/hcapmreportservice/api/apm/report/expire/view',

  //EC安装码
  validateECuser: '/hcapmreportservice/api/apm/report/ec-equip/user',
  getECinfo: '/hcapmreportservice/api/apm/report/ec-equip/captcha/',

  //设备调剂——设备预约
  reservationList: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/list',
  reservationHistory: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/history',
  reservationOften: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/oftenAssetInfoList',
  filterOption: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/getTypeList',
  reservation: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation',
  getReservationInfo: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/reservationBasicInfo',
  getConfigItem: '/hcapmmasterdataservice/api/apm/masterData/configItem',
  getDeviceConfig: '/hcapmmasterdataservice/api/apm/masterData/configItem/getByKeyAndSite',
  approvalReservation: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/confirmationReview ',

  //设备调剂——设备补录
  getAdditionalRecord: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/supplementaryRecord/detail',
  addRecord: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/supplementaryRecord',

  //设备调剂——消毒与清洁
  disinfectionAndCleanList: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/cleanedList',
  disinfectionAndCleanDetail: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/maintainDetail',
  convenientMaintenance: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/maintain',
  maintenanceInfo: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/maintainInfo',

  //设备调剂——我的预约
  myReservationConfig: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/unnit',
  myReservationList: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/unnitlist',
  cancelReservation: '/hcapmworkflowservice/api/apm/allocation/unmanned/reservation/cabcel',

  queryStock: '/hcapmworkflowservice/api/apm/allocation/queryStock',

  //game plan
  gpList: '/hcapmsalesgameplan/apm/sales/game/plan/msa/recommend/findOrgList',
  findUSUpgradeRecommendList: '/hcapmsalesgameplan/apm/sales/game/plan/us/upgrade/recommend/findUSUpgradeRecommendList',
  gpHospital: '/hcapmsalesgameplan/apm/sales/game/plan/msa/recommend/findAssetListByOrg',
  gpNumFlag: '/hcapmsalesgameplan/apm/sales/game/plan/msa/patient/flag',
  gpDetail: '/hcapmsalesgameplan/apm/sales/game/plan/msa/recommend',
  getCareDetail: '/hcapmsalesgameplan/apm/sales/game/plan/care/package',
  coilList: '/hcapmsalesgameplan/apm/sales/game/plan/coil/opportunity/queryByCoilInstalled',
  mrPicList: '/hcapmsalesgameplan/apm/sales/game/plan/coil/opportunity/queryImages',
  ibu: '/hcapmsalesgameplan/apm/sales/game/plan/ibu/info',
  usOption: '/hcapmsalesgameplan/apm/sales/game/plan/us/option',
  probe: '/hcapmsalesgameplan/apm/sales/game/plan/probe',
  tags: '/hcapmsalesgameplan/apm/sales/game/plan/coil/opportunity/getAllTags',
  ans: '/hcapmsalesgameplan/apm/sales/game/plan/ans/spare',
  gameplan: '/hcapmsalesgameplan/apm/sales/game/plan',

  //云上大咖
  getDeviceOrQuestionType: '/hcapmmasterdataservice/api/apm/masterData/i8nMessages',
  getQuestionList: `${quora}/list`,
  getQuestionDetail: `${quora}/detail`,
  publishQuestion: `${quora}/release`,
  setQuestionSample: `${quora}/set-sample`,
  setQuestionStatus: `${quora}`,

  sparePart: '/hcapmsalesgameplan/apm/sales/game/plan/spare/part',

  /** 备件相关 start */
  sparepartstotal: sparepartstotal + '/findAll',
  sparepartsrequestapply: sparepartsrequest + '/apply',
  sparepartsrequestbuy: sparepartsrequest + '/buy',
  sparepartsdetail: sparepartsrequest + '/sparepartsdetail',
  sparepartsname: sparepartsrequest + '/getPartsNames',

  backuplist: `${sparepartstotal}/findPartsInventory`,
  backup_apply: `${sparepartsrequest}/getPartsRequestByPage`,
  backup_warehouse: `${sparepartswarehouse}/findWarehouseListInRequest`,
  backup_detail: `${sparepartsrequest}/getPartsRequestDetail/{id}`,
  backup_new: `${sparepartsrequestEdit}/getPartsRequestCode/{type}`,
  backup_new_parts: `${sparepartstotal}/parsRequestGetPartsItemList/{type}`,
  backup_parts_by_type: `${sparepartstotal}/getInventoryNumAndAvgPriceByPartsItemId`,
  backup_parts_add: `${sparepartsrequestEdit}/insertPartsRequest`,
  backup_in_out_warehouse: `${sparepartswarehouse}/findWarehouseListInOrOutStock/{type}`,
  backup_parts_in_sotck: `${sparepartstock}/partsInStock`,
  backup_parts_out_stock: `${sparepartstock}/partsOutStock`,
  backup_psrts_config_location: `${sparepartsconfig}/getPartsConfig`,
  backup_psrts_get_location: `${sparepartswarehouse}/getPartsWareHouseLocatorListByPartsWareHouseId/{type}`,

  getPartsItemList: '/hcapmassetservice/api/apm/parts/item/getPartsItemList',
  getPartsItemInfo: '/hcapmassetservice/api/apm/parts/item/getPartsItemInfo',
  /** 备件相关 end */

  /** 审核相关 start*/
  approvalsWaitApproval: approvals + '/waitApproval',
  approvalsMyRequest: approvals + '/myRequest',
  approvalsMyAudited: approvals + '/myAudited',
  approvalsDetail: approvals + '/detail',
  approvalsAgree: approvals + '/agree',
  approvalsDisagree: approvals + '/disagree',
  approvalsReturn: approvals + '/return',
  approvalsDirect: approvals + '/directDelivery',
  approvalsConfig: approvals + '/config',
  approvalsSubmit: approvals + '/submit',

  assetScrap: workAsset + '/assetScrap',
  /** 审核相关 end*/

  /** 转科 */
  departmentConvert: workAsset + '/departmentConvert',

  /** 反馈feedback start*/
  handbookFeedback: `${report}/service-desk`,
  /** 反馈feedback end*/

  /** 文章信息查询 start*/
  articleInfo: '/hcapmcms/api/apm/cms/content/detail',
  /** 文章信息查询 end*/

  /** 不良事件上报 Adverse Events Up Report start*/
  adverseEvents: `${asset}/adverse/events`,
  reporterList: `${asset}/adverse/events/reporterList`, //上报人查询接口
  approveList: `${asset}/adverse/events/approveList`, //审批人查询接口
  allAdverseList: `${asset}/adverse/events/allAdverseList`, //审批人全院工单
  addAdverseEvent: `${asset}/adverse/events/addAdverseEvent`, //创建接口
  adverseEventsDetail: `${asset}/adverse/events/getDetail`, //获取详细信息
  approveAdverseEvent: `${asset}/adverse/events/approveAdverseEvent`, //审批接口
  invalidAdverseEvent: `${asset}/adverse/events/invalidAdverseEvent`, //作废接口
  approveStagingAdverseEvent: `${asset}/adverse/events/approveStagingAdverseEvent`, //审核人暂存接口
  buyerAdverseList: `${asset}/adverse/events/buyerAdverseList`, //采购员查询接口
  updateAdverseEvent: `${asset}/adverse/events/updateAdverseEvent`, //采购员提交接口
  sendMail: `${asset}/adverse/events/sendMail`, //邮件发送接口

  adverseEventsReportSummary: `${asset}/adverse/events/report/summary`, //上报员统计接口
  adverseEventsApproverSummary: `${asset}/adverse/events/approve/summary`, //审核员统计接口
  adverseEventsPurchaserSummary: `${asset}/adverse/events/buyer/summary`, //采购员统计接口

  /** 不良事件上报 Adverse Events Up Report end*/

  /** 用户账号审批 start*/
  getUserAccountApprovalHistory: `/hcapmselfservice/api/apm/selfservice/register/history`,
  getUserAccountPendingApproval: `/hcapmselfservice/api/apm/selfservice/register/pending`,
  userAccountBatchPass: `/hcapmselfservice/api/apm/selfservice/register/approval`,
  userAccountBatchReject: `/hcapmselfservice/api/apm/selfservice/register/reject`,
  userAccountBatchDelete: `/hcapmselfservice/api/apm/selfservice/register/delete`,
  APPROVAL_EDIT_SERVICE: `/hcapmselfservice/api/apm/selfservice/register/edit`,
  getDemoRegisterUrl: `/hcapmselfservice/api/apm/wechatsubscribemessage/geturlbykeyword?keyword=我要试用`,
  /** 用户账号审批 end*/

  /** 科室借调 Department Seconded start */
  createSeconded: '/hcapmworkflowservice/api/apm/siteToSiteAllocation',
  secondedInfoInProcess: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/open', //查询设备有没有在借调的过程中
  secondedInfo: '/hcapmworkflowservice/api/apm/siteToSiteAllocation',
  borrowerAcknowledge: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/ack/borrower',
  returneeAcknowledge: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/ack/returner',
  secondedSignature: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/ack/signature',
  secondedSummary: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/summary',
  getOrgByAssetUid: '/hcapmworkflowservice/api/apm/siteToSiteAllocation/getOrgByAssetUid',
  /** 科室借调 Department Seconded end */

  /*资源管理内容接口*/
  slidesList: `${report}/slides/list`,

  /*APM助手*/
  pointsGain: `${report}/points/gain`, //积分
  pointsDetail: `${report}/points/detail`,
  aigcGeneration: `${report}/aigc/generation`, //发消息
  historyList: `${report}/aigc/historyList`, //历史列表
  detailList: `${report}/aigc/detailList`, //历史详情
  deleteHistoryInf: `${report}/aigc/deleteHistoryInf`, //删除历史记录
  commentStatus: `${report}/aigc/commentStatus`, // 赞、踩
  creatShare: `${report}/aigc/share`, //生产分享ID
  getShareMessage: `${report}/aigc/shareMessage`, // 获取分享聊天记录
  stopGenerating: `${report}/aigc/stopGenerating`, // 停止问答提交记录
  assetEventsCZip: `${report}/assetLifecycleEvents/zip`, // creat 资产报告压缩文件
  assetEventsGZip: `${report}/assetLifecycleEvents/getObjectId` // get 资产报告压缩文件 ID
}

let urls = {}
const domains = { gateway }

for (let domain in domains) {
  const obj = domains[domain]
  Object.keys(obj).forEach(key => (obj[key] = '/' + domain + obj[key]))
  urls = Object.assign(urls, obj)
}

/** others, no proxy replace */
urls.getMedia = () => {
  const { wxUserInfo } = window.___store___.getState().preload
  if (wxUserInfo && wxUserInfo.corpUserId) {
    return 'https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=:token&media_id='
  } else {
    return 'https://api.weixin.qq.com/cgi-bin/media/get?access_token=:token&media_id='
  }
}
urls.pdfViewer = '/pdf/web/viewer.html'
urls.exeyDomain = 'edu.gehealthcare.cn'
urls.goGeEdu = `https://${urls.exeyDomain}/mobile/index`
urls.iconnect = 'http://www.gehcsservice.com/iconnect'
urls.oneClickRepair = 'http://www.gehcsservice.com/iconnect/index.php/OneAPMClickRepair/oneClickRepair.html'

if (BASE_URL) {
  urls.pdfViewer= BASE_URL + urls.pdfViewer
  urls.obj = BASE_URL + urls.obj
  urls.objDownload = BASE_URL + urls.objDownload
  urls.downloadByAuthCode = BASE_URL + urls.downloadByAuthCode
}

export default urls
