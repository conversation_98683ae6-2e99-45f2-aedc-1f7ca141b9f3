import React from 'react'
import isEqual from 'lodash/isEqual'
import { connect } from 'react-redux'
import { Button as <PERSON><PERSON><PERSON>, message, Checkbox as <PERSON>heck<PERSON>, Popover } from 'antd'
import { util, validators } from '../constants'
import { Select, SelectUser, SelectOffice, SelectOrg, SelectOrgNew, SelectFaulty, SelectSupplier, SelectInventoryDept, Dialog, ImageUploader, Audio, Rating, sign, MasterAsset, Attachment } from '../components'
import { Button, ButtonArea, CellBody, CellHeader, CellFooter, CellsTitle, Icon, Form, FormCell, Label as WLabel, Input, Switch, TextArea, Checkbox, Select as WSelect } from 'react-weui'
const Ref = props => <i style={{ display: 'block', fontSize: '70%', color: 'gray', textAlign: 'left' }}>{props.content}</i>
function FieldHistory(props) {
  const { list } = props;
  return (<div style={{
    maxHeight: '200px',
    maxWidth: ' 350px',
    overflow: 'auto'
  }}>
    <div style={{
      fontSize: '12px',
      color: '#999',
    }}>历史记录</div>
    {
      list.map((item, index) => {
        return (<div style={{
          lineHeight: '40px',
          borderBottom: '1px solid #e5e5e5'
        }} key={index} onClick={() => {
          props.onClick(item.value)
        }}>{item.value}</div>)
      })
    }
  </div>)
}
export const Label = props => {
  return <WLabel>{props.children}{props.item && <Ref content={props.item.ref} />}</WLabel>
}
export const UNSAVED_COLOR = '#00B9E6'
class GeneralForm extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      expand: {}, // 用于折叠group
      value: { ...props.value },
      error: {},
      history: {},
      tempValue: {}
    }
    this.eventBind = this.eventBind.bind(this)
    this.renderButtons = this.renderButtons.bind(this)
    this.handleSelectHistory = this.handleSelectHistory.bind(this)
  }

  componentWillMount() {
    this.setDefaultValues()
    this.formValidation()
  }

  componentWillReceiveProps(nextProps) {
    if (!isEqual(this.props.buttons, nextProps.buttons)) {
      this.renderButtons(nextProps.buttons)
    }
    if (!isEqual(this.props.groups, nextProps.groups)) {
      this.setState(this.state)
    }
  }

  async formValidation() {
    const { preload, groups } = this.props
    const fields = []
    if (!groups || !(groups instanceof Array)) {
      return
    }
    groups.forEach(group => group.items.forEach(item => fields.push(item)))
    const hideFilter = item => typeof item.hide === 'function'
      ? !item.hide(this.state.value)
      : true
    
    try {
      const error = await validators.validation(this.state.value, fields.filter(hideFilter), preload)
      if (error) {
        return new Promise(resolve => {
          this.setState({ error }, resolve)
        })
      } else {
        return new Promise(resolve => {
          this.setState({ error: {} }, resolve)
        })
      }
    } catch (err) {
      console.error('Validation error:', err)
    }
  }

  setDefaultValues() {
    const { groups, value } = this.props
    if (groups && groups instanceof Array) {
      groups.forEach(group => {
        const { items } = group
        if (items && items instanceof Array) {
          items.filter(item => item.key).forEach(item => {
            const defaultValue = item.props && item.props.defaultValue
            if (defaultValue && !value[item.key]) {
              this.state.value[item.key] = defaultValue
              this.setState(this.state)
            }
          })
        }
      })
    }
  }

  async eventBind(e) {
    const { onChange } = this.props
    if (e.target && e.target.name) {
      const newValue = {
        ...this.state.value,
        [e.target.name]: e.target.type === 'number' && e.target.value !== '' ? 
          this.fixNumber(e.target.value, e.target.getAttribute('decimal')) : 
          e.target.value
      }
      
      // 使用 Promise 确保状态更新和验证的顺序
      await new Promise(resolve => {
        this.setState({ value: newValue }, resolve)
      })
      
      await this.formValidation()
      
      if (onChange && typeof onChange === 'function') {
        onChange(newValue, e, value => this.setState({ value }))
      }
    }
  }

  fixNumber(value, decimalLength) {
    return (value && decimalLength && value % 1 != 0 && (value + '').split(".")[1].length > decimalLength) ? Number(value).toFixed(decimalLength) : value
  }

  eventBindTransform(e, transformer) {
    if (transformer) {
      if (e.target && e.target.value) {
        e.target.value = transformer(e.target.value)
      }
    }
    this.eventBind(e)
  }

  eventBindCheckbox(name, e) {
    const target = {
      name,
      value: this.state.value[name] || []
    }
    if (e.target && e.target.checked) {
      if (target.value.indexOf(e.target.value) < 0) {
        target.value.push(e.target.value)
      }
    } else if (e.target) {
      const pos = target.value.indexOf(e.target.value)
      if (pos > -1) {
        target.value.splice(pos, 1)
      }
    } else if (Array.isArray(e)) {
      target.value = e
    }
    this.eventBind({ target })
  }

  renderRating(item) {
    const { value, preload } = this.props
    let header = null
    if (item.label) {
      header = (
        <CellHeader>
          <Label item={item}>{preload.label(item, value)}</Label>
        </CellHeader>
      )
    }
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <FormCell warn={warn}>
        {header}
        <CellBody>
          <Rating
            {...item.props}
            onClick={rating => {
              this.eventBind({
                target: {
                  name: item.key,
                  value: rating
                }
              })
            }}
          />
        </CellBody>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
      </FormCell>
    )
  }
  handleSelectHistory(key, value, cacheKey) {
    this.setState({
      value: {
        ...this.state.value,
        [key]: value
      },
      history: {
        ...this.state.history,
        [cacheKey]: false
      }
    })
  }
  renderText(item) {

    let cacheKey = this.props.namespace ? `${this.props.namespace}/${item.key}` : item.key
    const { value, preload, dispatch, myself } = this.props
    const style = {}
    if (value[item.key] != this.state.value[item.key]) {
      style.color = UNSAVED_COLOR
    }
    let type
    let select
    if (item.options && item.options.length > 0 && item.options.every(opt => typeof opt === 'string')) {
      let filteredOptions = this.state.value[item.key]
        ? item.options.filter(option => option && option.indexOf(this.state.value[item.key]) > -1)
        : item.options
      select = (
        <WSelect onChange={this.eventBind} name={item.key}>
          <option key="first" value={''}>请选择</option>
          {filteredOptions.map((str, index) => <option key={index} value={str} selected={str == value[item.key]}>{str}&#x200E;</option>)}
        </WSelect>
      )
    }
    let clear
    if (item.component === 'Date' || item.component === 'DateTime') {
      clear = <Icon value="cancel" onClick={() => {
        this.state.value[item.key] = ''
        this.setState(this.state, this.formValidation)
        // this.setState(this.state, () => {
        //   delete this.state.value[item.key]
        //   this.setState(this.state, this.formValidation)
        // })
      }} />
    }
    switch (item.component) {
      case 'Date':
        type = 'date'
        break
      case 'DateTime':
        type = 'datetime-local'
        break
      case 'Number':
        type = 'number'
        this.state.value[item.key] = this.fixNumber(this.state.value[item.key], item.decimalLength)
        item.props = { max: Number.MAX_VALUE, decimal: item.decimalLength, inputMode: 'decimal', ...item.props }
        break
      default:
        type = 'text'
        item.props = { maxLength: 64, ...item.props }
    }
    const full = item.full == undefined ? false : item.full
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    const selectProps = select ? { select: true, selectPos: 'after' } : {}

    const onChange = (e) => {
      if (item.onlyEnglish && e.target.value.match(/[\u4e00-\u9fa5]/gm)) {
        message.warn('不允许输入中文')
        e.target.value = e.target.value.trim().replace(/[\u4e00-\u9fa5]/gm, "")
      }
      if (item.onlyNumber && e.target.value.match(/[^\d\-]/gm)) {
        message.warn('只允许输入数字和减号')
        e.target.value = e.target.value.trim().replace(/[^\d\-]/gm, "")
      }
      this.eventBind(e)
    }

    const onBlur = (e) => {

      let curTempValue = this.state.tempValue[cacheKey]
      if (item.removeBlank && e.target.value.match(/\s/)) {
        message.warn('不允许输入空格')
        e.target.value = e.target.value.trim().replace(/\s/g, "")
      }
      if (item.upperCase) {
        e.target.value = e.target.value.toUpperCase()
      }
      if (item.upperCase || item.removeBlank) {
        this.eventBind(e)
      }
      if (item.cache !== undefined) {
        this.setState({
          history: {
            ...this.state.history,
            [cacheKey]: false
          },
        })
      }
      if (item.cache !== undefined && e.target.value !== '' && curTempValue !== e.target.value) {
        /***
         * 3不存
         * 组件没开启缓存不存；
         * 输入值为空不存；
         * 输入值没变化不存；
         * * */
        dispatch({
          type: 'formCache/saveFieldCache',
          payload: {
            value: e.target.value,
            key: cacheKey,
            myself, // 传用户信息为了隔离各用户的历史记录
            cache: item.cache // 历史记录保存时长。单位毫秒例如60*60*1000
          }
        })
      }
    }
    const onFocus = (e) => {
      this.setState({
        tempValue: {
          ...this.state.tempValue,
          [cacheKey]: e.target.value
        }
      })
      if (item.cache !== undefined) {
        const uid = myself.userAccount.uid
        if (this.props.formCache[uid] && this.props.formCache[uid][cacheKey] && this.props.formCache[uid][cacheKey].length > 0) {
          // showHistory
          this.setState({
            history: {
              ...this.state.history,
              [cacheKey]: true
            },
          })
        }
      }
    }

    return (
      <FormCell warn={warn} key={`text_${item.key}`} {...selectProps}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        {!full && <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>}
        <CellBody>
          <Popover placement="bottomRight" visible={this.state.history[cacheKey] || false} content={<FieldHistory onClick={(value) => this.handleSelectHistory(item.key, value, cacheKey)} list={(this.props.formCache[this.props.myself.userAccount.uid] && this.props.formCache[this.props.myself.userAccount.uid][cacheKey] && this.props.formCache[this.props.myself.userAccount.uid][cacheKey].length > 0) ? this.props.formCache[this.props.myself.userAccount.uid][cacheKey] : []} />}>
            <Input
              autoComplete='off'
              name={item.key}
              type={type}
              style={style}
              defaultValue={preload.val(item, value)}
              value={this.state.value[item.key]}
              onChange={onChange}
              onBlur={(e) => onBlur(e)}
              onFocus={(e) => onFocus(e)}
              {...item.props}
            />
          </Popover>
        </CellBody>
        <CellFooter>
          {select}
          {this.state.value[item.key] && item.clearable && clear}
        </CellFooter>
      </FormCell>
    )
  }

  renderLabel(item) {
    const { value, preload } = this.props
    if (item.content) {
      return (
        <FormCell key={`label_${item.key}`}>
          <CellBody style={{ textAlign: 'left', maxWidth: '100%', ...item.style }}>{item.content}</CellBody>
        </FormCell>
      )
    } else if (item.children) {
      return (
        <FormCell key={`label_${item.key}`}>
          <CellBody style={{ textAlign: 'left', maxWidth: '100%', ...item.style }}>{item.content}</CellBody>
        </FormCell>
      )
    } else {
      return (
        <FormCell key={`label_${item.key}`}>
          <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
          <CellBody>{preload.val(item, value)}</CellBody>
        </FormCell>
      )
    }
  }

  renderSelect(item) {
    const { value, preload } = this.props
    let style = item.props && item.props.style ? item.props.style : {}
    if (value[item.key] != this.state.value[item.key]) {
      style = { ...style, color: UNSAVED_COLOR }
    }
    if (item.alias) {
      if (value[item.alias] != this.state.value[item.alias]) {
        style = { ...style, color: UNSAVED_COLOR }
      }
    }
    const props = {
      label: preload.label(item, value),
      style,
      onChange: (e, current) => {
        if (typeof item.transform === 'function') {
          e = item.transform(e)
        }
        let val = e && e.id ? e.id : e && e.target && e.target.value ? e.target.value : e
        if (item.component === 'SelectUser' && item.props && item.props.useUid && e.uid) {
          val = e.uid
        }
        // 先更新state.value
        this.eventBind({ target: { name: item.key, value: val } })
        // 再调用item.onChange（如有），并传递最新的value
        if (item.onChange) {
          // 注意：此时this.state.value还未更新，所以用setTimeout确保顺序
          setTimeout(() => {
            item.onChange(val, { ...this.state.value, [item.key]: val }, current, e)
          }, 0)
        }
        if (item.setValue && typeof item.setValue === 'function') {
          this.setState({ value: item.setValue(e, this.state.value) })
        }
      },
      getInput: input => {
        if (item.alias) {
          this.eventBind({ target: { name: item.alias, value: input } })
        }
      }
    }
    if (value[item.key] !== undefined) {
      props.defaultValue = value[item.key]
    }
    let Component
    switch (item.component) {
      case 'Select':
        Component = Select
        props.item = item
        props.value = this.state.value[item.key]
        if (item.query) {
          Object.keys(item.query).forEach(key => {
            const type = typeof item.query[key]
            if (type === 'string') {
              props.item[key] = this.state.value[item.query[key]]
            } else if (type === 'function') {
              props.item[key] = item.query[key](this.state.value)
            }
          })
        }
        break
      case 'SelectUser':
        Component = SelectUser
        item.query && Object.keys(item.query).forEach(key => props[key] = this.state.value[item.query[key]])
        break
      case 'SelectOffice':
        Component = SelectOffice
        break
      case 'SelectOrg':
        Component = SelectOrg
        break
      case 'SelectOrgNew':
        Component = SelectOrgNew
        break
      case 'SelectFaulty':
        Component = SelectFaulty
        break
      case 'SelectSupplier':
        Component = SelectSupplier
        break
      case 'SelectInventoryDept':
        Component = SelectInventoryDept
        break
      default:
        Component = Select
        props.item = item
    }

    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    const componentProps = { ...item.props, ...props }
    return <Component {...componentProps} warn={warn && this.renderError(item)} key={`select_${item.key}`} />
  }

  renderAttachment(item) {
    const { value, preload } = this.props
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    const onChange = items => {
      this.eventBind({ target: { name: item.key, value: items } })
    }
    return (
      <Attachment
        warn={warn && (this.state.error[item.key].join('\n'))}
        title={preload.label(item, value)}
        items={value[item.key]}
        onChange={onChange}
        {...item.props}
      />
    )
  }

  renderImageUploader(item) {
    const { value } = this.props
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <ImageUploader
        {...Object.assign({}, item.props, {
          onChange: images => {
            this.eventBind({ target: { name: item.key, value: images } })
            if (item.props && item.onChange) {
              item.props.onChange(images)
            }
          }
        })}
        files={value[item.key]}
        neat
        warn={warn && (this.state.error[item.key].join('\n'))}
        key={`image_uploader_${item.key}`}
      />
    )
  }

  renderAudio(item) {
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (<Audio
      {...item.props}
      onSave={audio => {
        this.eventBind({ target: { name: item.key, value: audio } })
      }}
      neat
      warn={warn && (this.state.error[item.key].join('\n'))}
      key={`audio_${item.key}`} />)
  }

  renderSwitch(item) {
    const { value, preload } = this.props
    const checked = typeof this.state.value[item.key] === 'string' ? this.state.value[item.key] === 'true' : this.state.value[item.key] || false
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <FormCell switch warn={warn} key={`switch_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
        <CellBody />
        <CellFooter style={{ transform: 'scale(0.8)' }}><Switch checked={checked} onChange={e => this.eventBind({ target: { name: item.key, value: e.target.checked } })} {...item.props} /></CellFooter>
      </FormCell>
    )
  }

  renderSwitch3(item) {
    const buttonConfig = {
      true: { type: 'primary', icon: 'check', style: { backgroundColor: '#00B9E6', borderColor: '#00B9E6' } },
      false: { type: 'normal', icon: 'close', style: { backgroundColor: 'red', borderColor: 'red', color: 'white' } },
      null: { type: 'normal', icon: '', style: {} }
    }

    const Switch3Button = (btnVal, clickVal) =>
      <AButton
        type={buttonConfig[btnVal].type}
        shape='circle'
        icon={buttonConfig[btnVal].icon}
        style={buttonConfig[btnVal].style}
        onClick={() => _onClick(clickVal)} />

    const { value, preload } = this.props
    const trueFalse = this.state.value[item.key]
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    let button
    const _onClick = (val) => {
      if (!item.props || !item.props.disabled) {
        const target = { name: item.key, value: val }
        this.eventBind({ target })
      }
    }

    let { options } = item.props || { options: [] }
    if (options && options.length === 2) {
      if (trueFalse === options[0]) {
        button = Switch3Button(options[0], options[1])
      } else {
        button = Switch3Button(options[1], options[0])
      }
    } else {
      if (trueFalse === true) {
        button = Switch3Button(true, false)
      } else if (trueFalse === false) {
        button = Switch3Button(false, null)
      } else {
        button = Switch3Button(null, true)
      }
    }

    return (
      <FormCell switch warn={warn} key={`switch_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
        <CellBody style={{ lineHeight: 0 }}>
          {button}
        </CellBody>
      </FormCell>
    )
  }

  renderSwitchLabel(item, defaultValue) {
    const { value, preload } = this.props
    const checked = this.state.value[item.key] == undefined ? defaultValue : this.state.value[item.key]
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <FormCell switch warn={warn} key={`switch_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
        <CellBody><Label style={{ paddingRight: '10px' }}>{checked ? '是' : '否'}</Label></CellBody>
        <CellFooter style={{ transform: 'scale(0.8)' }}><Switch checked={checked} onChange={e => this.eventBind({ target: { name: item.key, value: e.target.checked } })} {...item.props} /></CellFooter>
      </FormCell>
    )
  }

  renderCheckbox(item) {
    const { value, preload } = this.props
    const key = (Math.random() + 1).toString(36).substring(7)
    const checked = this.state.value[item.key] ? this.state.value[item.key].indexOf(item.value) > -1 : false
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    if (Array.isArray(item.options) && item.options.length > 0) {
      const value = this.state.value[item.key]
      return (
        <FormCell checkbox warn={warn} key={key}>
          {
            warn && <CellFooter>{this.renderError(item)}</CellFooter>
          }
          <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
          <CellBody>
            <ACheckbox.Group options={item.options} defaultValue={[]} value={value} onChange={e => this.eventBindCheckbox(item.key, e)} {...item.props} />
          </CellBody>
        </FormCell>)
    } else
      return (
        <FormCell checkbox warn={warn} key={key}>
          {
            warn && <CellFooter>{this.renderError(item)}</CellFooter>
          }
          <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
          <CellBody />
          <CellFooter>
            <Checkbox name={item.key} value={item.value} checked={checked} onChange={e => this.eventBindCheckbox(item.key, e)} {...item.props} />
          </CellFooter>
        </FormCell>
      )
  }

  renderTextArea(item) {
    const { value, preload } = this.props
    const style = {}
    if (value[item.key] != this.state.value[item.key]) {
      style.color = UNSAVED_COLOR
    }
    const full = item.full == undefined ? true : item.full
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <FormCell warn={warn} key={`textarea_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        {!full && <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>}
        <CellBody>
          <TextArea
            name={item.key}
            style={style}
            defaultValue={preload.val(item, value)}
            maxLength="200"
            onChange={this.eventBind}
            {...item.props}
            onClick={e => {
              if (typeof item.onClick === 'function') {
                item.onClick(this.state.value, e, value => {
                  this.setState({ value }, this.formValidation)
                })
              }
            }} />
          {full && <Ref content={item.ref} />}
        </CellBody>
      </FormCell>
    )
  }

  renderMasterAsset(item) {
    const itemValue = this.state.value[item.key]
    const onChange = retValue => {
      const event = { target: { value: retValue, name: item.key } }
      this.eventBindTransform(event, item.props.transform)
    }
    const { value, preload } = this.props
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    const query = getQuery(item.query, this.state.value)
    return (
      <FormCell warn={warn} key={`custom_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
        <CellBody>
          <MasterAsset {...item.props} value={itemValue} onChange={onChange} {...query} />
        </CellBody>
        <CellFooter />
      </FormCell>
    )
  }

  renderCustomizedElement(item) {
    const itemValue = this.state.value[item.key]
    const onChange = retValue => {
      const event = { target: { value: retValue, name: item.key } }
      this.eventBindTransform(event, item.props ? item.props.transform : null)
    }
    const query = getQuery(item.query, this.state.value)
    const Customized = React.createElement(item.component, Object.assign(item.props || {}, { value: itemValue, onChange }, query))
    const { value, preload } = this.props
    const warn = this.state.error[item.key] && this.state.error[item.key].length > 0
    return (
      <FormCell warn={warn} key={`custom_${item.key}`}>
        {
          warn &&
          <CellFooter>
            {this.renderError(item)}
          </CellFooter>
        }
        <CellHeader><Label item={item}>{preload.label(item, value)}</Label></CellHeader>
        <CellBody>{Customized}</CellBody>
        <CellFooter />
      </FormCell>
    )
  }

  renderElement(item) {
    const { value } = this.state
    const onChange = value => {
      this.eventBind({
        target: {
          name: item.key, value
        }
      })
    }
    return (
      <FormCell key={`element_${item.key}`}>
        {item.render({ item, value, props: this.props, onChange })}
      </FormCell>
    )
  }

  renderItems(items) {
    return items.map((item, index) => {
      if (item.hide && item.hide(this.state.value)) {
        return null
      }
      if (item.component && typeof item.component !== 'string') {
        return this.renderCustomizedElement(item)
      }
      switch (item.component) {
        case 'Attachment':
          return this.renderAttachment(item)
        case 'Element':
          return this.renderElement(item)
        case 'Text':
        case 'Date':
        case 'DateTime':
        case 'Number':
        case 'Tel':
        case 'Email':
          return this.renderText(item)
        case 'Label':
          return this.renderLabel(item)
        case 'Select':
        case 'SelectUser':
        case 'SelectFaulty':
        case 'SelectOffice':
        case 'SelectSupplier':
        case 'SelectInventoryDept':
        case 'SelectOrg':
        case 'SelectOrgNew':
          return this.renderSelect(item)
        case 'Switch':
          return this.renderSwitch(item)
        case 'Switch3':
          return this.renderSwitch3(item)
        case 'SwitchLabel':
          return this.renderSwitchLabel(item, false)
        case 'SwitchTrue':
          return this.renderSwitchLabel(item, true)
        case 'TextArea':
          return this.renderTextArea(item)
        case 'Checkbox':
          return this.renderCheckbox(item)
        case 'ImageUploader':
          return this.renderImageUploader(item)
        case 'Audio':
          return this.renderAudio(item)
        case 'Rating':
          return this.renderRating(item)
        case 'MasterAsset':
          return this.renderMasterAsset(item)
        default:
          return this.renderText(item)
      }
    })
  }

  renderButtons(buttons) {
    const { dispatch, myself } = this.props
    let modified = diff(this.props.value, this.state.value)
    return buttons.filter(item => item).map((btn, index) => { // 滤掉null的元素
      return (
        <Button
          style={{ overflow: 'unset' }}
          key={index}
          type={btn.type}
          disabled={(btn.confirm && btn.disabled === 'unchanged' && Object.keys(modified).length === 0) || btn.disabled === true}
          onClick={() => {
            this.formValidation()
            const { error } = this.state
            const submit = () => {
              if (btn.full) {
                btn.onClick({...this.props.value, ...modified })
              } else {
                btn.onClick(diff(this.props.value, this.state.value))
              }
            }
            if (btn.validation && Object.keys(error).length > 0) {
              Dialog.alert(this.renderErrors(error), '表单校验不通过')
            } else if (btn.confirm && btn.signature) {
              sign(btn.confirm, objectId => {
                this.eventBind({ target: { name: btn.signature, value: objectId } })
                submit()
              })
            } else if (btn.confirm) {
              Dialog.confirm(btn.confirm).onDismiss(confirmed => confirmed && submit())
            } else {
              submit()
            }
            dispatch({
              type: 'formCache/saveFormCacheToLocalStorage',
              payload: {
                myself
              }
            })
          }}
          {...btn.props}>
          {btn.label}
        </Button>
      )
    })
  }

  renderErrors(error) {
    const msgs = Object.keys(error).map(key => error[key].map(msg => <p>{msg}</p>))
    const style = msgs.length > 1 ? { marginBottom: '20px' } : {}
    return <div style={style}>{Object.keys(error).map(key => error[key].map((msg, index) => <p key={index}>{msg}</p>))}</div>
  }

  renderError(item) {
    const style = this.state.error[item.key].length > 1 ? { marginBottom: '20px' } : { marginTop: '20px' }
    const props = {
      desc: <div style={style}>{this.state.error[item.key].map((msg, index) => <p key={index}>{msg}</p>)}</div>,
      type: 'alert',
      title: '输入项不符合要求'
    }
    return <Dialog {...props}><Icon value="warn" /></Dialog>
  }

  render() {
    const { groups, buttons, onChange } = this.props
    const forms = []
    if (groups && groups instanceof Array) {
      groups.forEach((group, index) => {
        let Extra
        if (group.extra) {
          Extra = React.cloneElement(group.extra, {
            ...group.extra.props, onClick: () => {
              if (typeof group.extra.props.onClick === 'function') {
                group.extra.props.onClick()
              }
              if (typeof group.extra.props.onChange === 'function') {
                this.setState({ value: group.extra.props.onChange(this.state.value) }, this.formValidation)
              }
              if (typeof onChange === 'function') {
                onChange(this.state.value)
              }
            }
          })
        }
        const expand = val => this.setState({ expand: { ...this.state.expand, [index]: val } })
        if (group.title) {
          forms.push(
            <CellsTitle key={`title_${index}`} style={{ display: 'flex', justifyContent: 'space-between' }}>
              <ExpandTitle value={this.state.expand[index]} defaultValue={group.expand} onExpand={expand}>{group.title}</ExpandTitle>{Extra}
            </CellsTitle>
          )
        }
        const display = this.state.expand[index] !== undefined
          ? this.state.expand[index] === false
            ? 'none'
            : ''
          : group.expand === false
            ? 'none'
            : ''
        forms.push(
          <Form key={`form_${index}`} style={{ display }} checkbox={group.checkbox}>
            {this.renderItems(group.items)}
          </Form>
        )
      })
    }

    return (
      <div className="edit-form">
        {forms}
        {
          buttons && buttons.length > 0 && (
            <ButtonArea direction="horizontal">
              {this.renderButtons(buttons)}
            </ButtonArea>
          )
        }
      </div>
    )
  }
}

const ExpandTitle = props => {
  const { children, value, defaultValue, onExpand } = props
  const onClick = () => onExpand(value !== undefined ? !value : !defaultValue)
  const ExpandTrue = <div style={{ marginRight: '5px' }}>-</div>
  const ExpandFalse = <div style={{ marginRight: '5px' }}>+</div>
  const Expand =
    value !== undefined
      ? value === false
        ? ExpandFalse
        : ExpandTrue
      : defaultValue !== undefined
        ? defaultValue === false
          ? ExpandFalse
          : ExpandTrue
        : null

  return (
    <div style={{ display: 'flex' }} onClick={onClick}>
      {Expand} {children}
    </div>
  )
}

function getQuery(query, value) {
  let props = {}
  if (typeof query === 'object') {
    Object.keys(query).forEach(key => {
      const type = typeof query[key]
      if (type === 'string') {
        props[key] = value[query[key]]
      } else if (type === 'function') {
        props[key] = query[key](value)
      }
    })
  }
  return props
}

function diff(origin, current) {
  const obj = {}
  Object.keys(current).forEach(key => {
    if (current[key] !== origin[key])
      obj[key] = current[key]
  })
  return obj
}

function mapStateToProps(state) {
  return {
    preload: util.preload(state),
    myself: state.preload.myself,
    formCache: state.formCache
  }
}

export default connect(mapStateToProps)(GeneralForm)
