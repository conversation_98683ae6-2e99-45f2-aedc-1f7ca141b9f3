import React from 'react'
import PropTypes from 'prop-types'
import { FormCell, CellHeader, CellFooter, Label, CellBody, Input } from 'react-weui'
import { Select } from 'antd'
import { rest, urls } from '../../constants'
import { pick, isEqual } from 'lodash'
import './index.less'

const { Option } = Select
class SelectUser extends React.Component {

  constructor() {
    super()
    this.state = {
      loading: false,
      input: '',
      all: [],
      users: []
    }
  }

  componentWillMount() {
    const { posturl, databody, assetId, url, ...others } = this.props
    if (url || assetId) {
      this.setState({ loading: true })
      rest.get(url || `${urls.assetTag}/${assetId}/owners`).then(users => {
        this.setState({ users, all: users, loading: false }, this.setDefaultUser)
      })
    }else if(posturl || databody) {
      this.setState({ loading: true })
      rest.post(posturl, databody).then(users => {
        this.setState({ users, all: users, loading: false }, this.setDefaultUser)
      })
    } else if (others) {
      this.getOrgUsers(others)
    }
  }

  componentWillReceiveProps(nextProps) {
    const lastQuery = pick(this.props, ['tenantUID', 'institutionUID', 'hospitalUID', 'siteUID', 'orgInfoId', 'role', 'trace', 'orgUid', 'uid'])
    const nextQuery = pick(nextProps, ['tenantUID', 'institutionUID', 'hospitalUID', 'siteUID', 'orgInfoId', 'role', 'trace', 'orgUid', 'uid'])
    if (!isEqual(lastQuery, nextQuery)) {
      this.getOrgUsers(nextProps)
    }
  }


  getUserIdentity(user) {
    const { useUid = false } = this.props
    if (useUid) {
      return user.uid
    } else {
      return user.id
    }
  }

  getOrgUsers(others) {
    const query = pick(others, ['tenantUID', 'institutionUID', 'hospitalUID', 'siteUID', 'orgInfoId', 'role', 'trace', 'orgUid', 'uid'])
    let isEmpty = true
    Object.keys(query).forEach(key => {
      if (query[key]) {
        isEmpty = false
        return
      }
    })
    if (isEmpty) {
      this.setState({ users: [] })
    } else {
      rest.get(urls.usersFilter, query).then(users => {
        this.setState({ users, all: users }, this.setDefaultUser)
      })
    }
  }

  setDefaultUser() {
    const { all } = this.state
    const { defaultValue } = this.props
    if (Array.isArray(all)) {
      all.forEach(user => {
        if (this.getUserIdentity(user) === defaultValue) {
          this.setState({ input: user.name })
        }
      })
    }
  }

  renderOptions(excludes) {
    if (!Array.isArray(this.state.users) || this.state.users.length === 0) {
      return []
    }
    
    const ex = new Set(excludes)
    const userOptions = this.state.users.map((user, index) => {
      const userIdentity = this.getUserIdentity(user)
      if (ex.has(userIdentity)) {
        return null
      }
      return <Option key={index} value={userIdentity}>{user.name}&#x200E;</Option>
    }).filter(Boolean)

    return userOptions
  }

  eventChange = (value) => {
    const { onChange } = this.props
    const user = this.state.users.filter(user => this.getUserIdentity(user) == value)[0] || value
    const { name: input = '' } = user
    this.setState({ input, users: this.state.all })
    onChange(user)
  }

  eventFilter = (val) => {
    const input = val.trim()
    const { all } = this.state
    const re = new RegExp("^[a-zA-Z0-9]+$")
    if (input && re.test(input)) {
      const users = all.filter(user => user.namePyCode ? user.namePyCode.indexOf(input.toLowerCase()) > -1 : false)
      this.setState({ users })
    } else if (input) {
      const users = all.filter(user => user.name.indexOf(input) > -1)
      this.setState({ users })
    } else {
      this.setState({ users: this.state.all })
    }
    this.setState({ input })
  }

  render() {
    const { first, firstValue, firstLabel, label, disabled, excludes, neat, warn, defaultValue, ...others } = this.props
    const { loading } = this.state
    const select = (
      <Select
        className='custom-select'
        autoClearSearchValue={false}
        filterOption={false}
        loading={loading} 
        disabled={disabled} 
        showSearch 
        {...others} 
        onSearch={this.eventFilter} 
        defaultValue={defaultValue} 
        onChange={this.eventChange} 
        style={{ minWidth: 150, textOverflow: 'normal' }}
      >
        {!first && <Option value={firstValue || ''}>{firstLabel || '请选择'}&#x200E;</Option>}
        {this.renderOptions(excludes)}
      </Select>
    )
    return neat ? select : (
      <FormCell warn={warn && true}>
        {warn && <CellFooter>{warn}</CellFooter>}
        <CellHeader>
          <Label>{label}</Label>
        </CellHeader>
        <CellBody>
          {select}
        </CellBody>
        {/* <CellFooter>
          {select}
        </CellFooter> */}
      </FormCell>
    )
  }
}

SelectUser.propTypes = {
  assetId: PropTypes.any,
  label: PropTypes.string,
  defaultValue: PropTypes.string,
  onChange: PropTypes.func,
  excludes: PropTypes.array
}

export default SelectUser
